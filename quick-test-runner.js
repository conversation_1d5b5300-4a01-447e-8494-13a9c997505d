/**
 * Quick Test Runner for Trial Notification System
 * 
 * Copy and paste this entire script into the Chrome extension's background script console
 * to quickly test the trial notification functionality.
 */

console.log('🚀 Starting Quick Trial Notification Test...\n');

// Test configuration
const QUICK_TEST_CONFIG = {
    // Test intervals in milliseconds
    INTERVALS: {
        '25h': 25 * 60 * 60 * 1000,  // Should not trigger
        '23h': 23 * 60 * 60 * 1000,  // Should trigger 24h warning
        '11h': 11 * 60 * 60 * 1000,  // Should trigger 12h warning
        '5h': 5 * 60 * 60 * 1000,    // Should trigger 6h warning
        '30m': 30 * 60 * 1000,       // Should trigger 1h warning
        '10m': 10 * 60 * 1000,       // Should not trigger
    }
};

// Quick test functions
async function quickTestNotifications() {
    console.log('📱 Testing notification triggers...\n');
    
    // Clear any existing notification flags first
    await new Promise(resolve => {
        chrome.storage.local.remove(['trialNotificationsSent'], resolve);
    });
    
    console.log('🧹 Cleared existing notification flags\n');
    
    // Test each interval
    for (const [label, ms] of Object.entries(QUICK_TEST_CONFIG.INTERVALS)) {
        console.log(`⏰ Testing ${label} (${ms}ms remaining)...`);
        
        try {
            if (typeof checkTrialExpirationNotification === 'function') {
                await checkTrialExpirationNotification(ms);
                console.log(`✅ ${label} test completed`);
            } else {
                console.log(`❌ checkTrialExpirationNotification function not found`);
                break;
            }
        } catch (error) {
            console.error(`❌ Error testing ${label}:`, error);
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Check what notification flags were set
    chrome.storage.local.get(['trialNotificationsSent'], (result) => {
        console.log('\n📊 Notification flags set:', result.trialNotificationsSent || 'None');
    });
}

async function quickTestDuplicatePrevention() {
    console.log('\n🚫 Testing duplicate prevention...\n');
    
    // Set a flag manually
    await new Promise(resolve => {
        chrome.storage.local.set({
            'trialNotificationsSent': { '24h': true }
        }, resolve);
    });
    
    console.log('🏷️ Set 24h notification flag manually');
    
    // Try to trigger 24h notification again
    console.log('⏰ Attempting to trigger 24h notification again...');
    
    if (typeof checkTrialExpirationNotification === 'function') {
        await checkTrialExpirationNotification(23 * 60 * 60 * 1000);
        console.log('✅ Duplicate prevention test completed');
    } else {
        console.log('❌ checkTrialExpirationNotification function not found');
    }
}

async function quickTestFlagClearing() {
    console.log('\n🧹 Testing flag clearing on renewal...\n');
    
    // Set multiple flags
    await new Promise(resolve => {
        chrome.storage.local.set({
            'trialNotificationsSent': {
                '24h': true,
                '12h': true,
                '6h': true
            }
        }, resolve);
    });
    
    console.log('🏷️ Set multiple notification flags');
    
    // Trigger renewal scenario (more than 2 days)
    console.log('⏰ Triggering renewal scenario (3 days remaining)...');
    
    if (typeof checkTrialExpirationNotification === 'function') {
        await checkTrialExpirationNotification(3 * 24 * 60 * 60 * 1000);
        
        // Check if flags were cleared
        setTimeout(() => {
            chrome.storage.local.get(['trialNotificationsSent'], (result) => {
                const flags = result.trialNotificationsSent || {};
                const flagCount = Object.keys(flags).length;
                
                if (flagCount === 0) {
                    console.log('✅ Flags cleared correctly on renewal');
                } else {
                    console.log(`❌ Flags not cleared. Remaining: ${flagCount}`);
                }
            });
        }, 1000);
    } else {
        console.log('❌ checkTrialExpirationNotification function not found');
    }
}

async function quickTestAlarmSystem() {
    console.log('\n⏰ Testing alarm system...\n');
    
    // Check if trial notification alarm exists
    chrome.alarms.getAll((alarms) => {
        const trialAlarm = alarms.find(alarm => alarm.name === 'trialNotificationCheck');
        
        if (trialAlarm) {
            console.log('✅ Trial notification alarm found:', {
                name: trialAlarm.name,
                scheduledTime: new Date(trialAlarm.scheduledTime).toLocaleString(),
                periodInMinutes: trialAlarm.periodInMinutes
            });
        } else {
            console.log('❌ Trial notification alarm not found');
            console.log('Available alarms:', alarms.map(a => a.name));
        }
    });
    
    // Test manual trigger of trial notification check
    console.log('🔄 Testing manual trial notification check...');
    
    if (typeof performTrialNotificationCheck === 'function') {
        try {
            await performTrialNotificationCheck();
            console.log('✅ Manual trial notification check completed');
        } catch (error) {
            console.error('❌ Error in manual trial notification check:', error);
        }
    } else {
        console.log('❌ performTrialNotificationCheck function not found');
    }
}

async function quickTestPermissions() {
    console.log('\n🔐 Testing permissions...\n');
    
    // Check notifications permission
    chrome.permissions.contains({
        permissions: ['notifications']
    }, (result) => {
        if (result) {
            console.log('✅ Notifications permission granted');
        } else {
            console.log('❌ Notifications permission not granted');
        }
    });
    
    // Check if chrome.notifications API is available
    if (typeof chrome !== 'undefined' && chrome.notifications) {
        console.log('✅ Chrome notifications API available');
        
        // Test creating a simple notification
        chrome.notifications.create('test-notification', {
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'Test Notification',
            message: 'This is a test notification from the trial notification system.'
        }, (notificationId) => {
            if (chrome.runtime.lastError) {
                console.log('❌ Error creating test notification:', chrome.runtime.lastError);
            } else {
                console.log('✅ Test notification created successfully');
                
                // Clear the test notification after 3 seconds
                setTimeout(() => {
                    chrome.notifications.clear(notificationId);
                }, 3000);
            }
        });
    } else {
        console.log('❌ Chrome notifications API not available');
    }
}

async function quickTestCurrentTrialStatus() {
    console.log('\n📊 Checking current trial status...\n');
    
    // Check stored trial status
    chrome.storage.sync.get(['userStatus', 'email', 'trialUsed'], (result) => {
        console.log('Current stored status:', {
            userStatus: result.userStatus || 'Not set',
            email: result.email || 'Not set',
            trialUsed: result.trialUsed || false
        });
        
        if (result.userStatus === 'TrialActive') {
            console.log('✅ User is in active trial - notifications should work');
        } else {
            console.log('ℹ️ User not in active trial - some tests may not trigger real notifications');
        }
    });
    
    // Check notification flags
    chrome.storage.local.get(['trialNotificationsSent'], (result) => {
        const flags = result.trialNotificationsSent || {};
        const flagCount = Object.keys(flags).length;
        
        console.log(`Current notification flags: ${flagCount} set`);
        if (flagCount > 0) {
            console.log('Flags:', flags);
        }
    });
}

// Main test runner
async function runQuickTests() {
    console.log('🧪 QUICK TRIAL NOTIFICATION TEST SUITE\n');
    console.log('=====================================\n');
    
    try {
        // Run all quick tests
        await quickTestCurrentTrialStatus();
        await quickTestPermissions();
        await quickTestAlarmSystem();
        await quickTestNotifications();
        await quickTestDuplicatePrevention();
        await quickTestFlagClearing();
        
        console.log('\n🏁 Quick test suite completed!');
        console.log('\n💡 Tips:');
        console.log('- Check your system notifications for any trial expiration warnings');
        console.log('- Look for console logs showing notification creation');
        console.log('- Verify that duplicate notifications are prevented');
        console.log('- Test clicking notifications to ensure they open the subscription page');
        
    } catch (error) {
        console.error('❌ Error running quick tests:', error);
    }
}

// Auto-run the tests
runQuickTests();

// Export functions for manual use
window.quickTestFunctions = {
    runQuickTests,
    quickTestNotifications,
    quickTestDuplicatePrevention,
    quickTestFlagClearing,
    quickTestAlarmSystem,
    quickTestPermissions,
    quickTestCurrentTrialStatus
};

console.log('\n📚 Available functions for manual testing:');
console.log('- quickTestFunctions.runQuickTests() - Run all tests');
console.log('- quickTestFunctions.quickTestNotifications() - Test notification triggers');
console.log('- quickTestFunctions.quickTestDuplicatePrevention() - Test duplicate prevention');
console.log('- quickTestFunctions.quickTestFlagClearing() - Test flag clearing');
console.log('- quickTestFunctions.quickTestAlarmSystem() - Test alarm system');
console.log('- quickTestFunctions.quickTestPermissions() - Test permissions');
console.log('- quickTestFunctions.quickTestCurrentTrialStatus() - Check current status');
