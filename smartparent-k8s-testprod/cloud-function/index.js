// index.js

const express = require('express');
const path = require('path');
const axios = require('axios');
const cors = require('cors');
const functions = require('@google-cloud/functions-framework');
const morgan = require('morgan');
const stripeModule = require('stripe');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
// Import configuration module
const config = require('./config');
// Import new DB functions
const {
  pool,
  initializeDatabase,
  getInstallation,
  addInstallation,
  updateActivationTimestamp, // Import new function
  updateInstallationEmail,
  updateInstallationStatus,
  getInstallationsByEmail,
  addSurveyResponse, // Import new function
  startPremiumTrial // Import new function
} = require('./db');
require('dotenv').config();

// Log the current mode
console.log(`Server starting in ${config.isTestMode ? 'TEST' : 'PRODUCTION'} mode`);
console.log(`Server URL: ${config.serverUrl}`);

// Status Constants
const STATUS_FREE = 'Free'; // New permanent free tier
const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail'; // Legacy/Transitional - should ideally become Free quickly
const STATUS_TRIAL_ACTIVE = 'TrialActive'; // Premium trial active
const STATUS_TRIAL_EXPIRED = 'TrialExpired'; // Legacy/Transitional - should become Free
const STATUS_SUBSCRIBED = 'Subscribed'; // Premium subscription active
const TRIAL_PERIOD_MS = 7 * 24 * 60 * 60 * 1000; // 7 days for Premium trial

// Initialize Stripe with the appropriate key based on test mode
const stripe = stripeModule(config.stripeSecretKey);
console.log(`Using Stripe in ${config.stripeSecretKey.startsWith('sk_test_') ? 'TEST' : 'LIVE'} mode`);
// Removed misplaced catch block

const app = express();

// Security middleware to block common probe attempts
app.use((req, res, next) => {
  // List of paths commonly probed by attackers
  const suspiciousPaths = [
    '/.env',
    '/docker/.env',
    '/.git',
    '/wp-admin',
    '/wp-login',
    '/administrator',
    '/admin',
    '/backup',
    '/db',
    '/.DS_Store',
    '/config.php',
    '/phpinfo.php',
    '/phpmyadmin'
  ];

  const path = req.path.toLowerCase();
  if (suspiciousPaths.some(blockedPath => path.includes(blockedPath))) {
    // Log security probe attempt
    console.error(`Security probe attempt blocked: ${req.method} ${req.path} from ${req.ip}`);
    // Return 403 instead of 404 to indicate we actively block these requests
    return res.status(403).json({
      error: 'Access denied'
    });
  }

  next();
});

// Enhanced CORS Configuration
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add CORS pre-flight middleware
app.options('*', cors());

// Logging Middleware with enhanced health check filtering
app.use(morgan('combined', {
  skip: (req) => {
    // Skip logging for health check probes and /check endpoint
    const ua = (req.headers['user-agent'] || '').toLowerCase();
    const isHealthCheck = ua.includes('kube-probe') ||
                         req.path === '/check' ||
                         req.path === '/healthz' ||
                         req.path === '/ready';
    return isHealthCheck;
  }
}));

// Disable console.log for health check endpoints
const originalLog = console.log;
console.log = function() {
  const stack = new Error().stack;
  if (stack.includes('/check') || stack.includes('/healthz') || stack.includes('/ready')) {
    return;
  }
  originalLog.apply(console, arguments);
};

// Middleware to Parse JSON Bodies and Capture Raw Body for Webhooks
app.use(
  express.json({
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  })
);

// Serve Static Files
app.use(express.static(path.join(__dirname, 'public')));

// Serve staticHosting folder
app.use('/staticHosting', express.static(path.join(__dirname, 'public', 'staticHosting')));

// Root Route
app.get('/', (req, res) => {
  console.log('Received GET request at /');
  res.send('Server is running');
});

// Get PLAN_PRICE_IDS from config module
const PLAN_PRICE_IDS = config.getPlanPriceIds();

// Debug endpoint to check plan IDs and Stripe configuration
app.get('/debug-plans', (req, res) => {
  console.log('Received GET request at /debug-plans');

  // Check if the Stripe key is a test key
  const isTestKey = config.stripeSecretKey.startsWith('sk_test_');

  // Get test and production price IDs from config
  const TEST_PRICE_IDS = config.getTestPriceIds();
  const PRODUCTION_PRICE_IDS = config.getProductionPriceIds();

  res.json({
    testMode: config.isTestMode,
    planPriceIds: PLAN_PRICE_IDS,
    TEST_PRICE_IDS: TEST_PRICE_IDS,
    PRODUCTION_PRICE_IDS: PRODUCTION_PRICE_IDS,
    stripeMode: isTestKey ? 'test' : 'live',
    serverUrl: config.serverUrl,
    // Only show the first 8 characters of the key for security
    stripeKeyPrefix: config.stripeSecretKey.substring(0, 8) + '...',
    // Additional information for debugging
    stripeTestMode: isTestKey,
    serverTestMode: config.isTestMode,
    stripeSecretKeyIsTest: config.stripeSecretKey.startsWith('sk_test_'),
    testModeInfo: "When test mode is enabled, the server uses Stripe test keys and test price IDs."
  });
});

// Verify Subscription Endpoint
app.post('/verify-subscription', async (req, res) => {
  try {
    const { email } = req.body;
    console.log('Verifying subscription for email:', email);

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }

    // Check subscription status in Stripe
    const customers = await stripe.customers.list({
      email: email,
      limit: 1,
      expand: ['data.subscriptions']
    });

    let isSubscribed = false;
    let subscriptionDetails = null;

    if (customers.data.length > 0) {
      const customer = customers.data[0];
      console.log('Found customer:', customer.id);

      // Get all subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Check all statuses
        expand: ['data.plan']
      });

      console.log('Customer subscriptions:', subscriptions.data.map(sub => ({
        id: sub.id,
        status: sub.status,
        current_period_end: new Date(sub.current_period_end * 1000)
      })));

      // Check for an active or trialing subscription specifically
      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        isSubscribed = true;
        subscriptionDetails = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log('Active subscription found:', subscriptionDetails);
      } else {
        console.log('No active or trialing subscription found for customer:', customer.id);
      }
    } else {
      console.log('No customer found for email:', email);
    }

    res.json({
      subscribed: isSubscribed,
      subscription: subscriptionDetails
    });
  } catch (error) {
    console.error('Error in /verify-subscription:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Endpoint to create a Stripe Checkout session
app.post('/create-checkout-session', async (req, res) => {
  try {
    console.log('Received checkout request body:', JSON.stringify(req.body));
    console.log('Request headers:', JSON.stringify(req.headers));

    const { email, plan, priceId: directPriceId, testMode: clientTestMode } = req.body;

    // Log the raw request body for debugging
    console.log('Raw request body:', req.rawBody);

    // Validate plan parameter
    if (!plan) {
      console.error('Missing plan parameter in request');
      return res.status(400).json({
        error: 'Missing plan parameter',
        details: 'The plan parameter is required and must be either "monthly" or "annual"'
      });
    }

    if (plan !== 'monthly' && plan !== 'annual') {
      console.error(`Invalid plan parameter: "${plan}". Must be "monthly" or "annual"`);
      return res.status(400).json({
        error: 'Invalid plan parameter',
        details: `Received "${plan}" but plan must be either "monthly" or "annual"`
      });
    }

    // Determine which test mode to use:
    // 1. If server is in test mode (config.isTestMode is true), always use test mode
    // 2. If server is in production mode but client requests test mode, use test mode
    // 3. Otherwise, use production mode
    const useTestMode = config.isTestMode || clientTestMode === true;

    console.log('Checkout request details:', {
      email,
      plan,
      directPriceId,
      clientTestMode,
      serverTestMode: config.isTestMode,
      finalTestMode: useTestMode,
      stripeKeyPrefix: config.stripeSecretKey.substring(0, 8) + '...',
      stripeKeyIsTest: config.stripeSecretKey.startsWith('sk_test_')
    });
    console.log('Available plans:', PLAN_PRICE_IDS);
    console.log(`Using Stripe in ${useTestMode ? 'TEST' : 'PRODUCTION'} mode`);

    // Get test and production price IDs from config
    const TEST_PRICE_IDS = config.getTestPriceIds();
    const PRODUCTION_PRICE_IDS = config.getProductionPriceIds();

    console.log('Test price IDs:', TEST_PRICE_IDS);
    console.log('Production price IDs:', PRODUCTION_PRICE_IDS);

    if (!email) {
      return res.status(400).json({ error: 'Missing email parameter' });
    }

    // Use the direct price ID if provided, otherwise look it up based on the plan and test mode
    let priceId = directPriceId;

    if (!priceId) {
      // We've already validated that plan is either 'monthly' or 'annual'
      console.log(`Selecting price ID for plan: "${plan}" in ${useTestMode ? 'TEST' : 'PRODUCTION'} mode`);

      if (useTestMode) {
        // Use test mode price IDs
        if (plan === 'monthly') {
          priceId = TEST_PRICE_IDS.monthly;
          console.log('Using test mode monthly price ID:', priceId);
        } else if (plan === 'annual') {
          priceId = TEST_PRICE_IDS.annual;
          console.log('Using test mode annual price ID:', priceId);
        } else {
          // This should never happen due to earlier validation
          console.error(`Unexpected plan value: "${plan}" after validation`);
          return res.status(400).json({
            error: 'Invalid plan parameter',
            details: `Received "${plan}" but plan must be either "monthly" or "annual"`
          });
        }
      } else {
        // Use production price IDs
        if (plan === 'monthly') {
          priceId = PRODUCTION_PRICE_IDS.monthly;
          console.log('Using production monthly price ID:', priceId);
        } else if (plan === 'annual') {
          priceId = PRODUCTION_PRICE_IDS.annual;
          console.log('Using production annual price ID:', priceId);
        } else {
          // This should never happen due to earlier validation
          console.error(`Unexpected plan value: "${plan}" after validation`);
          return res.status(400).json({
            error: 'Invalid plan parameter',
            details: `Received "${plan}" but plan must be either "monthly" or "annual"`
          });
        }
      }
    }

    // If we still don't have a price ID, return an error
    if (!priceId) {
      console.error('Failed to determine price ID for plan:', plan);
      console.error('Available plans:', PLAN_PRICE_IDS);
      console.error('Test price IDs:', TEST_PRICE_IDS);
      console.error('Production price IDs:', PRODUCTION_PRICE_IDS);

      // Add more detailed debugging information
      const debugInfo = {
        receivedPlan: plan,
        availablePlans: Object.keys(PLAN_PRICE_IDS),
        testMode: useTestMode,
        serverTestMode: config.isTestMode,
        clientTestMode: clientTestMode,
        stripeKeyIsTest: config.stripeSecretKey.startsWith('sk_test_'),
        requestHeaders: req.headers,
        requestOrigin: req.headers.origin || 'unknown',
        requestMethod: req.method,
        requestPath: req.path,
        requestQuery: req.query
      };

      console.error('Detailed debug info for price ID failure:', debugInfo);

      return res.status(400).json({
        error: 'Invalid subscription plan selected',
        details: debugInfo
      });
    }

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      customer_email: email,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${config.serverUrl}/staticHosting/success.html?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${config.serverUrl}/staticHosting/cancel.html`,
      billing_address_collection: 'auto',
      metadata: {
        email: email,
        plan: plan,
      },
    });

    res.json({ url: session.url });
    console.log(`Checkout session created for ${email}, plan: ${plan}, session URL: ${session.url}`);
  } catch (error) {
    console.error('Error in /create-checkout-session:', error);

    // Provide more detailed error information
    let errorMessage = 'Internal server error';
    let statusCode = 500;

    if (error.type === 'StripeInvalidRequestError') {
      console.error('Stripe invalid request error:', error.message);
      errorMessage = `Stripe error: ${error.message}`;
      statusCode = 400;
    } else if (error.type === 'StripeAuthenticationError') {
      console.error('Stripe authentication error:', error.message);
      errorMessage = 'Payment service authentication error';
      statusCode = 500;
    } else if (error.type === 'StripeAPIError') {
      console.error('Stripe API error:', error.message);
      errorMessage = 'Payment service API error';
      statusCode = 500;
    } else if (error.type === 'StripeConnectionError') {
      console.error('Stripe connection error:', error.message);
      errorMessage = 'Payment service connection error';
      statusCode = 500;
    } else if (error.type === 'StripeRateLimitError') {
      console.error('Stripe rate limit error:', error.message);
      errorMessage = 'Payment service rate limit exceeded';
      statusCode = 429;
    }

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});


// --- Helper Function for Stripe Subscription Check ---
/**
 * Checks if an email has an active Stripe subscription.
 * Includes timeout handling.
 * @param {string} email - The customer's email address.
 * @returns {Promise<{isSubscribed: boolean, subscriptionDetails: object|null}>}
 */
async function checkSubscription(email) {
  if (!email) {
    return { isSubscribed: false, subscriptionDetails: null };
  }

  console.log(`Checking Stripe subscription for: ${email}`);
  try {
    // Timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Stripe API timeout')), config.timeout || 10000) // Default 10s timeout
    );

    // Stripe API call promise
    const stripePromise = async () => {
      const customers = await stripe.customers.list({
        email: email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        console.log(`Stripe: No customer found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }

      const customer = customers.data[0];
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Fetch all to potentially log inactive ones if needed
        expand: ['data.plan'],
      });

      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        const details = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log(`Stripe: Active subscription found for ${email}`, details);
        return { isSubscribed: true, subscriptionDetails: details };
      } else {
        console.log(`Stripe: No active/trialing subscription found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }
    };

    // Race the promises
    return await Promise.race([stripePromise(), timeoutPromise]);

  } catch (error) {
    console.error(`Error checking Stripe subscription for ${email}:`, error);
    // Don't block user flow on Stripe error, assume not subscribed
    return { isSubscribed: false, subscriptionDetails: null };
  }
}


// Stripe webhook endpoint
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = config.stripeWebhookSecret;

  let event;

  console.log('Received webhook:', req.rawBody);

  try {
    event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
    console.log('Webhook event constructed:', event.type);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // --- Helper function to update status for all installations linked to an email ---
  const updateStatusByEmail = async (email, newStatus) => {
    if (!email) {
      console.error(`Webhook: Cannot update status (${newStatus}) without an email.`);
      return;
    }
    try {
      const installations = await getInstallationsByEmail(email);
      if (installations.length === 0) {
        console.log(`Webhook: No installations found for email ${email} to update status to ${newStatus}.`);
        return;
      }
      console.log(`Webhook: Found ${installations.length} installation(s) for email ${email}. Updating status to ${newStatus}.`);
      // Update status for all found installations
      const updatePromises = installations.map(inst => updateInstallationStatus(inst.ip_hash, newStatus));
      await Promise.all(updatePromises);
      console.log(`Webhook: Successfully updated status to ${newStatus} for installations associated with ${email}.`);
    } catch (dbError) {
      console.error(`Webhook: Database error updating status to ${newStatus} for email ${email}:`, dbError);
      // Decide if we should throw or just log
    }
  };

  // --- Handle different event types ---
  let customerEmail = null;
  let customerId = null;
  const eventData = event.data.object;

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('Webhook: Handling checkout.session.completed');
        // Prefer email from metadata if available, fallback to customer details
        customerEmail = eventData.metadata?.email || eventData.customer_details?.email;
        if (customerEmail) {
          await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
        } else {
          console.error('Webhook: checkout.session.completed - Could not determine email from session metadata or customer details.');
        }
        break;

      case 'customer.subscription.created':
        console.log('Webhook: Handling customer.subscription.created');
        // Need to retrieve customer email using customer ID
        customerId = eventData.customer;
        if (customerId) {
          try {
            const customer = await stripe.customers.retrieve(customerId);
            customerEmail = customer.email;
            if (customerEmail) {
              await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
            } else {
               console.error(`Webhook: customer.subscription.created - Customer ${customerId} has no email.`);
            }
          } catch (stripeError) {
             console.error(`Webhook: customer.subscription.created - Error retrieving customer ${customerId}:`, stripeError);
          }
        } else {
           console.error('Webhook: customer.subscription.created - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.updated':
        console.log('Webhook: Handling customer.subscription.updated');
        const subscriptionStatus = eventData.status;
        console.log(`Webhook: Subscription status is now ${subscriptionStatus}`);
        // Need customer email
        customerId = eventData.customer;
        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                // Check if the subscription is active
                if (subscriptionStatus === 'active' || subscriptionStatus === 'trialing') {
                  await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
                } else {
                  // Assume inactive if not active/trialing (e.g., canceled, unpaid, past_due)
                  console.log(`Webhook: Subscription for ${customerEmail} is inactive (${subscriptionStatus}). Setting status to TrialExpired.`);
                  await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
                }
             } else {
                console.error(`Webhook: customer.subscription.updated - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: customer.subscription.updated - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error('Webhook: customer.subscription.updated - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.deleted':
      case 'invoice.payment_failed':
        console.log(`Webhook: Handling ${event.type}`);
        // Need customer email
        customerId = eventData.customer; // For subscription object
        if (!customerId && eventData.object === 'invoice') { // For invoice object
            customerId = eventData.customer;
        }

        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                console.log(`Webhook: Subscription inactive event (${event.type}) for ${customerEmail}. Setting status to TrialExpired.`);
                await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
             } else {
                console.error(`Webhook: ${event.type} - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: ${event.type} - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error(`Webhook: ${event.type} - Missing customer ID in event data.`);
        }
        break;

      case 'billing.alert.triggered':
        // Log but take no action on status
        console.log('Webhook: Received billing.alert.triggered. No status change needed.');
        break;

      default:
        console.log(`Webhook: Unhandled event type ${event.type}`);
    }
  } catch (error) {
      console.error(`Webhook: Error processing event ${event.type}:`, error);
      // Decide if we should return 500 to signal Stripe to retry (if applicable)
      // For now, just log and return 200 to acknowledge receipt.
  }

  // Return a 200 response to acknowledge receipt of the event
  res.status(200).end();
});

// Utility function for delay
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Analyze content using Grok API with retry mechanism
async function analyzeContent(url) {
  let lastError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt + 1} to analyze content for URL: ${url}`);

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API timeout')), config.timeout);
      });

      // Create the API call promise
      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Analyze the following URL for inappropriate content. Respond **only** with a JSON object containing two boolean fields: 'is_unsafe' and 'contains_games'. For example: {\"is_unsafe\": true, \"contains_games\": false}. 'is_unsafe' should be true if the content includes violent, bloody, horror, or pornographic content. If not, but it contains games or entertainment contents (such as videos, animations, novels, pictures, etc), 'contains_games' should be true."
          },
          {
            role: 'user',
            content: `Analyze the following URL: ${url}`
          }
        ],
        model: 'grok-2-latest',
        stream: false,
        temperature: 0
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);
      const content = response.data.choices[0].message.content.trim();
      console.log(`Raw AI response (attempt ${attempt + 1}):`, content);

      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonString = jsonMatch[0];
        const responseData = JSON.parse(jsonString);
        return {
          is_safe: !responseData.is_unsafe,
          contains_games: responseData.contains_games,
        };
      } else {
        throw new Error(`Invalid JSON response: ${content}`);
      }
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt + 1} failed:`, error);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All attempts failed. Last error:', lastError);
  throw new Error(`Failed to analyze content after ${config.maxRetries + 1} attempts: ${lastError.message}`);
}

// --- Authorization Middleware (Example - adapt as needed) ---
// This function determines the user's status and attaches it to the request.
// It will be used by endpoints needing authorization like /check.
async function determineUserStatus(req, res, next) {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    req.ip_hash = crypto.createHash('sha256').update(ip).digest('hex'); // Attach hash to request

    let installation = await getInstallation(req.ip_hash);
    let currentStatus = STATUS_FREE; // Default to Free if unknown or expired
    let isSubscribed = false;
    let userEmail = null;
    let trialUsed = false; // Initialize trial_used flag

    if (!installation) {
      // First contact, create record with Free status
      installation = await addInstallation(req.ip_hash, STATUS_FREE);
      currentStatus = STATUS_FREE;
    } else {
      userEmail = installation.email; // Get email if exists
      trialUsed = installation.trial_used || false; // Get trial_used status, default to false if null/undefined
      console.log(`[determineUserStatus] Found existing installation for ${req.ip_hash}. DB Status: ${installation.status}, Email: ${userEmail}, TrialUsed: ${trialUsed}`);

      // --- Prioritize DB Subscription Status ---
      if (installation.status === STATUS_SUBSCRIBED) {
          console.log(`[determineUserStatus] DB status is Subscribed for ${req.ip_hash}. Final Status: ${STATUS_SUBSCRIBED}`);
          currentStatus = STATUS_SUBSCRIBED;
          isSubscribed = true;
          // Optional: Verify with Stripe here if you want extra assurance, but trust the DB first.
          // const subCheck = await checkSubscription(userEmail);
          // if (!subCheck.isSubscribed) { /* Log potential inconsistency */ }
      } else if (installation.status === STATUS_TRIAL_ACTIVE) {
          // --- Handle Active Trial ---
          // Use activation_timestamp if available and valid, otherwise fall back to install_timestamp
          const trialStartDate = installation.activation_timestamp instanceof Date ? installation.activation_timestamp : (installation.install_timestamp instanceof Date ? installation.install_timestamp : null);
          const now = new Date();
          const trialExpired = trialStartDate ? (now.getTime() - trialStartDate.getTime()) > TRIAL_PERIOD_MS : true; // Assume expired if no valid start date

          if (!trialExpired) {
              // Still within Premium trial period
              console.log(`[determineUserStatus] DB status is TrialActive (Not Expired) for ${req.ip_hash}. Final Status: ${STATUS_TRIAL_ACTIVE}`);
              currentStatus = STATUS_TRIAL_ACTIVE;
              isSubscribed = false; // Explicitly false during trial
          } else {
              // Premium trial period is over
              console.log(`[determineUserStatus] DB status is TrialActive (Expired) for ${req.ip_hash}. Checking Stripe.`);
              if (userEmail) {
                  const subCheck = await checkSubscription(userEmail);
                  if (subCheck.isSubscribed) {
                      console.log(`[determineUserStatus] Stripe check confirms subscription for ${req.ip_hash} after trial expiry. Updating DB. Final Status: ${STATUS_SUBSCRIBED}`);
                      currentStatus = STATUS_SUBSCRIBED;
                      isSubscribed = true;
                      await updateInstallationStatus(req.ip_hash, STATUS_SUBSCRIBED);
                  } else {
                      console.log(`[determineUserStatus] No active subscription found for ${req.ip_hash} after trial expiry. Reverting to Free. Final Status: ${STATUS_FREE}`);
                      currentStatus = STATUS_FREE;
                      isSubscribed = false;
                      await updateInstallationStatus(req.ip_hash, STATUS_FREE);
                  }
              } else {
                  // Trial expired, but somehow no email was recorded? Revert to Free.
                  console.warn(`[determineUserStatus] Premium trial expired for ${req.ip_hash} but no email found. Reverting to Free. Final Status: ${STATUS_FREE}`);
                  currentStatus = STATUS_FREE;
                  isSubscribed = false;
                  await updateInstallationStatus(req.ip_hash, STATUS_FREE);
              }
          }
      } else {
          // --- Handle Free, TrialPendingEmail, TrialExpired (Treat all as potentially Free) ---
          console.log(`[determineUserStatus] DB status is ${installation.status} for ${req.ip_hash}. Checking Stripe if email exists.`);
          // Check Stripe only if an email exists
          if (userEmail) {
              const subCheck = await checkSubscription(userEmail);
              if (subCheck.isSubscribed) {
                  console.log(`[determineUserStatus] Stripe check confirms subscription for ${req.ip_hash}. Updating DB. Final Status: ${STATUS_SUBSCRIBED}`);
                  currentStatus = STATUS_SUBSCRIBED;
                  isSubscribed = true;
                  await updateInstallationStatus(req.ip_hash, STATUS_SUBSCRIBED);
              } else {
                  // Not subscribed via Stripe, ensure status is Free
                  console.log(`[determineUserStatus] Stripe check found no subscription for ${req.ip_hash}. Ensuring status is Free. Final Status: ${STATUS_FREE}`);
                  currentStatus = STATUS_FREE;
                  isSubscribed = false;
                  if (installation.status !== STATUS_FREE) {
                      console.log(`[determineUserStatus] Updating DB status to Free (was ${installation.status}).`);
                      await updateInstallationStatus(req.ip_hash, STATUS_FREE);
                  }
              }
          } else {
              // No email, ensure status is Free
              console.log(`[determineUserStatus] No email found for ${req.ip_hash}. Ensuring status is Free. Final Status: ${STATUS_FREE}`);
              currentStatus = STATUS_FREE;
              isSubscribed = false;
              if (installation.status !== STATUS_FREE) {
                   console.log(`[determineUserStatus] Updating DB status to Free (was ${installation.status}).`);
                   await updateInstallationStatus(req.ip_hash, STATUS_FREE);
              }
          }
      }
    }

     // Calculate remaining trial milliseconds ONLY if status is TrialActive
     let remainingMs = 0;
     if (currentStatus === STATUS_TRIAL_ACTIVE) {
         // Use activation_timestamp if available and valid, otherwise fall back to install_timestamp
         const trialStartDate = installation?.activation_timestamp instanceof Date ? installation.activation_timestamp : (installation?.install_timestamp instanceof Date ? installation.install_timestamp : null);
         if (trialStartDate) {
             const now = new Date();
             const trialEndDate = new Date(trialStartDate.getTime() + TRIAL_PERIOD_MS);
             remainingMs = Math.max(0, trialEndDate.getTime() - now.getTime()); // Ensure non-negative
         } else {
             console.warn(`Could not determine valid trial start date for ${req.ip_hash} during remainingMs calculation.`);
         }
     }

     // Attach status info to the request object for downstream handlers
     req.userStatus = {
       status: currentStatus,
       subscribed: isSubscribed,
       email: userEmail,
       ip_hash: req.ip_hash,
       trialUsed: trialUsed, // Include trialUsed flag
       remainingMs: currentStatus === STATUS_TRIAL_ACTIVE ? remainingMs : undefined // Only include if trial active
     };
     console.log(`Status for ${req.ip_hash}:`, req.userStatus);
    next();

  } catch (err) {
    console.error('Error in determineUserStatus middleware:', err);
    // Don't block, but maybe return a default "denied" status? Or let endpoint handle?
    // For now, let the endpoint decide based on missing req.userStatus
     req.userStatus = { status: 'Error', subscribed: false, email: null, ip_hash: req.ip_hash };
     next(); // Allow endpoint to handle error status
  }
}


// Enhanced /check endpoint with authorization
app.post('/check', determineUserStatus, async (req, res) => { // Added determineUserStatus middleware
  try {
    const { url } = req.body;
    const { status } = req.userStatus || { status: 'Error' }; // Get status from middleware

    console.log(`Received /check request for URL: ${url} from ${req.ip_hash} with status: ${status}`);

    // --- Authorization Check --- Allow Free users access ---
    if (status !== STATUS_FREE && status !== STATUS_TRIAL_ACTIVE && status !== STATUS_SUBSCRIBED) {
      console.log(`Unauthorized /check attempt by ${req.ip_hash} (status: ${status})`);
      // Return 403 Forbidden if status is invalid or errored
      return res.status(403).json({ error: 'Access denied. Invalid status.' });
    }
    // --- End Authorization Check ---

    if (!url) {
      return res.status(400).json({ error: 'URL is required.' });
    }

    try {
      const analysis = await analyzeContent(url);
      console.log('Analysis result:', analysis);
      res.json(analysis);
    } catch (error) {
      console.error('Analysis error:', error);
      // Send a more specific error response
      if (error.message.includes('timeout')) {
        res.status(504).json({ error: 'Analysis timed out. Please try again.' });
      } else {
        res.status(500).json({
          error: 'Analysis failed',
          details: error.message
        });
      }
    }
  } catch (error) {
    console.error('Error in /check:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Initialize nodemailer transporter globally using config
const transporter = nodemailer.createTransport({
  host: config.smtpHost,
  port: config.smtpPort,
  secure: config.smtpSecure,
  auth: {
    user: config.fromEmail,
    pass: config.emailPassword,
  }
});

// Verify SMTP configuration on startup
transporter.verify()
  .then(() => console.log('SMTP configuration is correct'))
  .catch(err => console.error('SMTP configuration error:', err));

app.post('/send-password-email', async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required.' });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    await transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Your Password for SmartParent',
      text: `Hello,

Your new password for SmartParent is: ${password}

Best regards,
SmartParent Support Team
`,
    });

    console.log(`Password email sent to ${email}`);
    res.json({ success: true });
  } catch (err) {
    console.error('Error sending password email:', err);
    res.status(500).json({ error: 'Failed to send email.' });
  }
});

// --- New Endpoints ---

// Simplified /log-install (now just ensures a record exists)
app.post('/log-install', async (req, res) => {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    const hashedIP = crypto.createHash('sha256').update(ip).digest('hex');

    let installation = await getInstallation(hashedIP);

    if (!installation) {
      // Add with FREE status directly
      await addInstallation(hashedIP, STATUS_FREE);
      console.log('Installation logged (new) for IP hash:', hashedIP, 'with status:', STATUS_FREE);
    } else {
      console.log('Installation log request for existing IP hash:', hashedIP);
      // Optionally update 'updated_at' timestamp implicitly via trigger if needed
    }
    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error logging installation:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// Renamed /check-trial to /status and using determineUserStatus middleware
app.get('/status', determineUserStatus, async (req, res) => {
  try {
    // The determineUserStatus middleware already calculated the status
    // and attached it to req.userStatus
    if (!req.userStatus) {
         // Should not happen if middleware is working, but handle defensively
         console.error('User status not found in /status endpoint for IP hash:', req.ip_hash);
         return res.status(500).json({ error: 'Failed to determine user status.' });
    }

    console.log('Responding to /status for', req.ip_hash, 'with:', req.userStatus);
    res.json(req.userStatus);

  } catch (err) {
    // This catch block might be redundant if middleware handles errors,
    // but keep for safety.
    console.error('Error in /status endpoint:', err);
    res.status(500).json({ error: 'Internal server error checking status' });
  }
});


// Updated /activate endpoint - Now primarily links email, doesn't start trial automatically
app.post('/activate', determineUserStatus, async (req, res) => {
  try {
    const { email } = req.body;
    // Get status, ip_hash, and potentially existing email from middleware
    const { status, ip_hash, email: existingEmail, subscribed: isAlreadySubscribed } = req.userStatus;

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }
     // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    console.log(`[/activate] Activation attempt for ${ip_hash} with email ${email}. Status from middleware: ${status}`);

    // Update email regardless of status, if different from existing
    if (email !== existingEmail) {
        console.log(`[/activate] Updating email for ${ip_hash} from ${existingEmail || 'none'} to ${email}`);
        await updateInstallationEmail(ip_hash, email);
    } else {
        console.log(`[/activate] Email ${email} already associated with ${ip_hash}.`);
    }

    // Determine the final status based *only* on Stripe check for the provided email
    let finalStatus;
    let finalSubscribed;

    console.log(`[/activate] Checking Stripe subscription status for activation email: ${email}`);
    const subCheck = await checkSubscription(email);

    if (subCheck.isSubscribed) {
        console.log(`[/activate] Stripe confirmed active subscription for ${email}. Setting final status to Subscribed.`);
        finalStatus = STATUS_SUBSCRIBED;
        finalSubscribed = true;
        // Ensure DB status matches
        // Use 'status' from middleware here to check if an update is needed
        if (status !== STATUS_SUBSCRIBED) {
             console.log(`[/activate] Updating DB status to Subscribed (was ${status}).`);
             await updateInstallationStatus(ip_hash, STATUS_SUBSCRIBED);
        }
    } else {
        console.log(`[/activate] No active Stripe subscription found for ${email}. Setting final status to Free.`);
        finalStatus = STATUS_FREE;
        finalSubscribed = false;
        // Ensure DB status is Free, regardless of previous state (TrialActive, Pending, Expired)
        // Use 'status' from middleware here to check if an update is needed
        if (status !== STATUS_FREE) {
            console.log(`[/activate] Updating DB status to Free (was ${status}).`);
            await updateInstallationStatus(ip_hash, STATUS_FREE);
        }
    }

    // Fetch the latest installation details to get trialUsed flag
    // Re-fetch installation data *after* potential status update
    const finalInstallationData = await getInstallation(ip_hash);
    const trialUsed = finalInstallationData?.trial_used || false; // Use re-fetched data

    console.log(`[/activate] Responding with: Status=${finalStatus}, Subscribed=${finalSubscribed}, Email=${email}, TrialUsed=${trialUsed}`);
    // Return the determined status, email, and trial info
    res.json({
        status: finalStatus,
        subscribed: finalSubscribed,
        email: email, // Return the email they just provided/confirmed
        trialUsed: trialUsed,
        // remainingMs is not relevant for /activate response anymore
    });

  } catch (err) {
    console.error('[/activate] Error during activation process:', err);
    res.status(500).json({ error: 'Internal server error during activation.' });
  }
});


// --- New Endpoint to Start Premium Trial ---
app.post('/start-trial', determineUserStatus, async (req, res) => {
    try {
        const { status, ip_hash, email, trialUsed } = req.userStatus;

        console.log(`Trial start attempt for ${ip_hash}, status: ${status}, trial used: ${trialUsed}`);

        if (status === STATUS_SUBSCRIBED) {
            return res.status(400).json({ error: 'Already subscribed.', status: status, subscribed: true });
        }
        if (status === STATUS_TRIAL_ACTIVE) {
            return res.status(400).json({ error: 'Trial already active.', status: status, subscribed: false, remainingMs: req.userStatus.remainingMs });
        }
        if (trialUsed) {
            return res.status(400).json({ error: 'Trial has already been used.', status: status, subscribed: false, trialUsed: true });
        }
        if (!email) {
             return res.status(400).json({ error: 'Email activation required before starting trial.', status: status, subscribed: false });
        }

        // Conditions met: User is Free (or legacy), hasn't used trial, and has activated email
        if (status === STATUS_FREE || status === STATUS_TRIAL_PENDING_EMAIL || status === STATUS_TRIAL_EXPIRED) {
            console.log(`Starting Premium trial for ${ip_hash}`);
            // Update status to TrialActive, mark trial as used, and set activation timestamp (acts as trial start time)
            await updateInstallationStatus(ip_hash, STATUS_TRIAL_ACTIVE);
            // Explicitly update trial_used flag - requires adding this function to db.js
            // Assuming a function like updateTrialUsedStatus(ip_hash, true) exists or is added
            // For now, let's assume we need to add it. Placeholder:
            // Use the new db function to start the trial
            const updatedInstallation = await startPremiumTrial(ip_hash);

            if (!updatedInstallation) {
                // Handle case where the update failed (e.g., installation not found)
                console.error(`Failed to start trial for ${ip_hash} using startPremiumTrial function.`);
                return res.status(500).json({ error: 'Failed to update trial status.' });
            }

            // Calculate remaining time immediately after starting
            const now = new Date();
            const trialEndDate = new Date(now.getTime() + TRIAL_PERIOD_MS);
            const remainingMs = Math.max(0, trialEndDate.getTime() - now.getTime());

            res.json({
                status: STATUS_TRIAL_ACTIVE,
                subscribed: false,
                email: email,
                trialUsed: true, // Trial is now used
                remainingMs: remainingMs
            });
        } else {
            // Should not happen if logic above is correct, but catch unexpected states
            console.error(`Unexpected status '${status}' during trial start for ${ip_hash}`);
            res.status(500).json({ error: 'Cannot start trial in the current state.' });
        }

    } catch (err) {
        console.error('Error starting trial:', err);
        res.status(500).json({ error: 'Internal server error starting trial.' });
    }
});


// --- New Endpoint for Uninstall Survey Submission ---
app.post('/submit-survey', async (req, res) => {
  try {
    const { reason, details, email, userStatus, usageDurationDays: clientUsageDays } = req.body; // Extract all provided data
    const ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    const hashedIP = crypto.createHash('sha256').update(ip).digest('hex');

    console.log(`Received survey submission from IP Hash: ${hashedIP}, Email: ${email || 'N/A'}, Status: ${userStatus || 'N/A'}, Reason: ${reason}`);

    if (!reason) {
      return res.status(400).json({ error: 'Reason is required.' });
    }

    let installation = null;
    let activationTimestamp = null;
    let usageDurationDays = clientUsageDays; // Use client-calculated value if provided

    // If client didn't provide usage duration, try to calculate from server data
    if (usageDurationDays === null || usageDurationDays === undefined) {
        // Try to find user by email first (more reliable), fallback to IP hash
        if (email) {
          const installations = await getInstallationsByEmail(email);
          // If multiple installations share an email, use the one with the latest install/activation?
          // For simplicity, let's take the first one found that has an activation timestamp.
          installation = installations.find(inst => inst.activation_timestamp) || installations[0];
        }
        // If no email provided or no match found, try IP hash (less reliable)
        if (!installation && hashedIP) {
           installation = await getInstallation(hashedIP);
        }

        if (installation && installation.activation_timestamp) {
           activationTimestamp = installation.activation_timestamp;
           const now = new Date();
           const diffTime = Math.abs(now - activationTimestamp);
           usageDurationDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
           console.log(`Server calculated usage duration: ${usageDurationDays} days for ${email || hashedIP}`);
        } else {
            console.log(`Could not find activation timestamp for ${email || hashedIP} to calculate duration.`);
        }
    } else {
        console.log(`Using client-provided usage duration: ${usageDurationDays} days for ${email || hashedIP}`);
    }

    // Save the survey response
    const surveyResponse = await addSurveyResponse(
      email || null,
      hashedIP,
      reason,
      details || null,
      usageDurationDays
    );

    // Send notification email to admin/developer
    try {
        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'; // Get admin email from env or default
        await transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: adminEmail,
            subject: 'SmartParent Uninstall Survey Feedback Received',
            html: `
                <h2>New Uninstall Feedback Received</h2>
                <p><strong>Timestamp:</strong> ${new Date(surveyResponse.submission_timestamp).toLocaleString()}</p>
                <p><strong>User Email:</strong> ${email || 'Not Provided'}</p>
                <p><strong>User Status:</strong> ${userStatus || 'Not Provided'}</p>
                <p><strong>IP Hash:</strong> ${hashedIP}</p>
                <p><strong>Uninstall Reason:</strong> ${reason}</p>
                <p><strong>Details:</strong></p>
                <pre>${details || 'N/A'}</pre>
                <p><strong>Calculated Usage (days):</strong> ${usageDurationDays !== null ? usageDurationDays : 'N/A'}</p>
                <hr>
                <p><em>Raw Response ID: ${surveyResponse.response_id}</em></p>
            `
        });
        console.log(`Survey notification email sent to ${adminEmail}`);
    } catch (emailError) {
        console.error('Error sending survey notification email:', emailError);
        // Don't fail the main request if notification fails
    }


    res.status(200).json({ success: true, message: 'Survey submitted successfully.' });
  } catch (err) {
    // Log the specific error encountered
    console.error('Error processing survey submission:', err.message, err.stack);
    // Optionally log the payload that caused the error (be mindful of sensitive data if any)
    // console.error('Survey Payload:', { reason, details, email, hashedIP });
    res.status(500).json({ error: 'Internal server error processing survey.' });
  }
});


// Add new endpoint for sending history email (apply authorization middleware)
app.post('/send-history-email', determineUserStatus, async (req, res) => { // Added determineUserStatus
    try {
        // User status is now available in req.userStatus
        const { status, subscribed, email: userEmailFromStatus } = req.userStatus || {}; // Get status from middleware

        // --- Authorization Check ---
        // Only allow TrialActive or Subscribed users to receive the email
        if (status !== STATUS_TRIAL_ACTIVE && status !== STATUS_SUBSCRIBED) {
            console.log(`Unauthorized /send-history-email attempt by ${req.ip_hash} (status: ${status})`);
            // It's okay to just return 200 OK here, as the extension might call this routinely.
            // The important part is not sending the email.
            return res.status(200).json({ success: true, message: 'User not active, email not sent.' });
        }
        // --- End Authorization Check ---

        const { email, history } = req.body; // Email in body might be redundant now, but keep for compatibility?
        // Prioritize email identified by middleware if available
        const targetEmail = userEmailFromStatus || email;
        console.log('Processing history email request for:', targetEmail);

        if (!targetEmail || !Array.isArray(history)) { // Ensure history is an array and we have an email
            console.error('Missing target email or invalid history data for /send-history-email');
            return res.status(400).json({ error: 'Valid email and history array are required.' });
        }

        // --- Step 3: Sort history by timeSpent (descending) ---
        history.sort((a, b) => (b.timeSpent || 0) - (a.timeSpent || 0));

        // --- Step 5: Generate AI Summary ---
        let aiSummaryHTML = '';
        try {
            // Prepare data for AI (e.g., top 5 sites)
            const historyHighlights = history.slice(0, 5).map(item =>
                `- ${item.url} (Title: ${item.title || 'Untitled'}) - ${formatTime(item.timeSpent || 0)}`
            ).join('\n');

            if (historyHighlights && config.grokApiUrl && config.grokApiKey) {
                console.log('Requesting AI summary for history highlights...');
                const aiPrompt = `Please analyze the following summary of today's web browsing history and generate a short summary paragraph followed by 2-3 brief bullet points of advice or points for discussion for a parent.

Browsing History Highlights (Sites visited, sorted by time spent):
${historyHighlights}

Based on this history:
1. Provide a brief overview of the main types of activity (e.g., learning, entertainment).
2. Mention the sites where the most time was spent.
3. Offer concise recommendations or discussion points for the parent (e.g., "Encourage continued use of educational sites," "Discuss the types of videos watched on YouTube," "Note the time spent on gaming sites").
4. Keep the entire response suitable for direct inclusion in an email, using simple paragraph and bullet point formatting. Respond only with the summary and advice content, without any introductory phrases like "Here is the summary:".`;

                // Use a timeout for the AI call
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('AI summary generation timeout')), config.timeout || 8000)
                );

                const aiApiPromise = axios.post(config.grokApiUrl, {
                    messages: [
                        {
                            role: 'system',
                            content: "You are an AI assistant helping parents understand their child's daily web browsing activity based on data from the SmartParent monitoring tool. Your goal is to provide a concise, helpful, and objective summary with brief, actionable advice. Avoid overly technical jargon and maintain a supportive tone. Do not invent information not present in the provided history. Focus on patterns and significant time spent."
                        },
                        {
                            role: 'user',
                            content: aiPrompt
                        }
                    ],
                    model: 'grok-2-latest', // Or the appropriate model
                    stream: false,
                    temperature: 0.5 // Allow for some variability in summary
                }, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.grokApiKey}`
                    },
                    timeout: config.timeout || 8000
                });

                const response = await Promise.race([aiApiPromise, timeoutPromise]);
                const summaryText = response.data.choices[0].message.content.trim();

                // Basic formatting for HTML email
                const formattedSummary = summaryText
                    .replace(/\n\n/g, '</p><p>') // Double newline to paragraph
                    .replace(/\n- /g, '<li>')    // Bullet points
                    .replace(/$/gm, (match, offset, str) => (str.substring(offset-4) === '<li>') ? '</li>' : '') // Close list items
                    .replace(/<li>/g, '<ul><li>') // Start list on first item
                    .replace(/<\/li>(?!<li>)/g, '</li></ul>'); // Close list after last item

                aiSummaryHTML = `
                    <div style="margin-bottom: 25px; padding: 15px; border: 1px solid #eee; background-color: #f9f9f9; border-radius: 5px; font-family: sans-serif; line-height: 1.6;">
                        <h3 style="margin-top: 0; color: #333;">Today's Browsing Summary & Advice</h3>
                        <p>${formattedSummary}</p>
                        <p style="font-size: 0.9em; color: #777; margin-top: 15px;">
                            <em>Note: This summary is AI-generated based on browsing patterns and may not capture all nuances.</em>
                        </p>
                    </div>
                    <hr style="border: none; border-top: 1px solid #eee; margin-bottom: 25px;">
                `;
                console.log('AI summary generated successfully.');
            } else {
                 console.log('Skipping AI summary generation (missing data or config).');
            }
        } catch (aiError) {
            console.error('Error generating AI summary:', aiError.message);
            // Don't fail the email, just proceed without the summary
            aiSummaryHTML = '<p style="color: red; font-style: italic;">Could not generate AI summary at this time.</p>';
        }

        // --- Step 4: Enhance Table Styling & Build HTML ---
        const historyHTML = history.map(item => `
            <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">${item.title || 'Untitled'}</td>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;"><a href="${item.url}" style="color: #007bff; text-decoration: none;">${item.url}</a></td>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: center;">${item.visitCount || 1}</td>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">${formatTime(item.timeSpent || 0)}</td>
            </tr>
        `).join('');

        // --- Step 2: Change Title ---
        const emailHTML = `
            <div style="font-family: Arial, sans-serif; color: #333;">
                ${aiSummaryHTML}
                <h2 style="color: #444;">Browsing History for ${new Date().toLocaleDateString()}</h2>
                <table style="width: 100%; border-collapse: collapse; margin-top: 15px; font-size: 0.95em;">
                    <thead>
                        <tr style="background-color: #f2f2f2; font-weight: bold;">
                            <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: left;">Title</th>
                            <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: left;">URL</th>
                            <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: center;">Visits</th>
                            <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: right;">Time Spent</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${historyHTML}
                    </tbody>
                </table>
            </div>
        `;

        // --- Step 1: Change Subject ---
        await transporter.sendMail({
            from: config.fromEmail || '<EMAIL>', // Use configured FROM or fallback
            to: email,
            subject: "SmartParent - Today's Browsing History", // Updated Subject
            html: emailHTML
        });

        console.log('History email sent successfully to:', email);
        res.json({ success: true });
    } catch (err) {
        console.error('Error sending history email:', err);
        // Check if headers already sent before sending response
        if (!res.headersSent) {
            res.status(500).json({ error: 'Failed to send history email.' });
        }
    }
});

// Helper function to format seconds into readable time
function formatTime(seconds) {
    // Ensure seconds is a non-negative number
    seconds = Math.max(0, Number(seconds) || 0);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
}

// Health check endpoint for Kubernetes probes
app.get('/check', (req, res) => {
  // Silent health check - status is logged by morgan middleware
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// ACME Challenge endpoint for Let's Encrypt validation
app.get('/.well-known/acme-challenge/:token', (req, res) => {
  res.status(200).send('OK');
});

// Fallback route for undefined paths
app.use((req, res) => {
  // Don't log 404s for known security probe paths
  const isSuspiciousPath = req.path.toLowerCase().includes('/.env') ||
                          req.path.toLowerCase().includes('/docker') ||
                          req.path.toLowerCase().includes('/.git') ||
                          req.path.toLowerCase().includes('/wp-');

  if (!isSuspiciousPath) {
    console.log(`Received ${req.method} request at ${req.originalUrl} - 404`);
  }
  res.status(404).send('Page not found');
});

// Export the Express app as the Cloud Function
functions.http('AIWebMon', app);
