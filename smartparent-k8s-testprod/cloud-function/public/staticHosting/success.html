<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Success - SmartParent</title>
    <style>
        /* CSS Variables for consistent styling - Matched with popup.css */
        :root {
            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Secondary Colors */
            --secondary-color: #5e35b1;
            --secondary-light: #7e57c2;
            --secondary-dark: #4527a0;
            --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

            /* Accent Colors */
            --accent-color: #f4b400;
            --accent-light: #ffc107;
            --accent-dark: #e69c00;
            --accent-gradient: linear-gradient(135deg, #f4b400 0%, #e69c00 100%);

            /* Status Colors */
            --success-color: #0f9d58;
            --success-light: #4caf50;
            --success-bg: rgba(15, 157, 88, 0.08);
            --error-color: #db4437;
            --error-light: #ef5350;
            --error-bg: rgba(219, 68, 55, 0.08);
            --warning-color: #f4b400;
            --info-color: #4285f4;

            /* Neutral Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --text-lighter: #80868b;
            --border-color: #dadce0;
            --border-light: #f1f3f4;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;
            --hover-bg: rgba(66, 133, 244, 0.08);
            --premium-bg: #e8f0fe;
            --dialog-bg: #fef7e0;
            --modal-bg: #e6f4ea;
            --subscribe-bg: #fce8e6;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --button-radius: 8px;
            --input-radius: 8px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            background-image:
                url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234285f4' fill-opacity='0.15'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background-color: var(--container-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
            border: 1px solid var(--border-light);
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--success-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .success-icon::before {
            content: "✓";
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: var(--text-color);
            line-height: 1.8;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            p {
                font-size: 1em;
            }

            .success-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon"></div>
        <h1>Subscription Successful!</h1>
        <p>Thank you for subscribing to SmartParent. Your subscription has been successfully processed, and your account has been activated with full access to all features.</p>
        <p>You can now close this window and continue using SmartParent with your full subscription benefits.</p>
        <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>
</body>
</html>
