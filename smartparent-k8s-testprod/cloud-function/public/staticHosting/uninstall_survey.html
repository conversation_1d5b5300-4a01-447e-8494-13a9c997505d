<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <title>SmartParent Uninstall Feedback</title>
    <style>
        /* CSS Variables for consistent styling - Matched with subscribe.css */
        :root {
            /* Force Light Theme */
            color-scheme: light;

            /* Primary Colors */
            --primary-color: #4285f4;
            --primary-light: #5a95f5;
            --primary-dark: #3367d6;
            --primary-gradient: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);

            /* Secondary Colors */
            --secondary-color: #5e35b1;
            --secondary-light: #7e57c2;
            --secondary-dark: #4527a0;
            --secondary-gradient: linear-gradient(135deg, #5e35b1 0%, #4527a0 100%);

            /* Status Colors */
            --success-color: #0f9d58;
            --success-light: #4caf50;
            --success-bg: rgba(15, 157, 88, 0.08);
            --error-color: #db4437;
            --error-light: #ef5350;
            --error-bg: rgba(219, 68, 55, 0.08);
            --warning-color: #f4b400;
            --info-color: #4285f4;

            /* Neutral Colors */
            --text-color: #202124;
            --text-light: #5f6368;
            --text-lighter: #80868b;
            --border-color: #dadce0;
            --border-light: #f1f3f4;

            /* Background Colors */
            --background-color: #e8f0fe;
            --container-bg: #f8f9fa;
            --hover-bg: rgba(66, 133, 244, 0.08);
            --premium-bg: #e8f0fe;
            --premium-box-bg: #d0e1fc;
            --dialog-bg: #fef7e0;
            --modal-bg: #e6f4ea;
            --subscribe-bg: #fce8e6;

            /* UI Elements */
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.05);
            --button-radius: 8px;
            --input-radius: 8px;
        }

        /* Disable dark mode completely */
        @media (prefers-color-scheme: dark) {
            /* This media query is intentionally left empty to prevent dark mode */
        }

        body {
            font-family: 'Arial', 'Helvetica Neue', 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 0;
            background-color: white;
            color: var(--text-color);
            line-height: 1.6;
        }

        header {
            background: var(--primary-gradient);
            color: #fff;
            padding: 50px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(66, 133, 244, 0.3);
            margin-bottom: 40px;
            position: relative;
        }

        header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto 40px;
            padding: 30px;
            background-color: var(--premium-bg);
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%234285f4' fill-opacity='0.15' fill-rule='evenodd'/%3E%3C/svg%3E");
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(66, 133, 244, 0.2);
        }

        .form-container {
            background-color: var(--premium-box-bg);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(66, 133, 244, 0.2);
            margin-top: 20px;
        }

        h2 {
            color: var(--primary-color);
            text-align: center;
            font-size: 1.8em;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }

        h2::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        p {
            font-size: 1.1em;
            color: var(--text-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 10px;
            background-color: white;
            border-radius: var(--input-radius);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .radio-option:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
        }

        input[type="radio"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        label {
            font-size: 1.1em;
            color: var(--text-color);
            cursor: pointer;
            flex: 1;
        }

        textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--input-radius);
            font-size: 1.1em;
            font-family: inherit;
            resize: vertical;
            min-height: 120px;
            transition: all 0.3s ease;
            background-color: white;
            color: var(--text-color);
            margin-top: 10px;
        }

        textarea:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 4px 8px rgba(66, 133, 244, 0.15);
        }

        button {
            padding: 16px 30px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--button-radius);
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin: 20px auto;
            display: block;
            min-width: 200px;
            font-family: 'Arial', 'Helvetica Neue', sans-serif;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4);
            filter: brightness(1.1);
        }

        button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            filter: none;
        }

        #submit-status {
            text-align: center;
            font-weight: 600;
            margin: 15px 0;
            padding: 10px;
            border-radius: var(--input-radius);
        }

        footer {
            background: var(--primary-gradient);
            color: #fff;
            padding: 30px 0;
            text-align: center;
            margin-top: 60px;
            box-shadow: 0 -4px 12px rgba(0,0,0,0.1);
        }

        footer p {
            margin: 0;
            opacity: 0.9;
            color: white;
        }

        @media screen and (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 0 15px 30px;
            }

            header h1 {
                font-size: 2em;
            }

            button {
                width: 100%;
            }
        }
    </style>
    <script>
        // Force light mode
        document.documentElement.setAttribute('data-color-scheme', 'light');
    </script>
</head>
<body>
    <header>
        <h1>Sorry to see you go!</h1>
    </header>

    <div class="container">
        <p>Thank you for trying SmartParent. Your feedback is valuable and helps us improve our service.</p>
        <h2>Why did you uninstall SmartParent?</h2>

        <div class="form-container">
            <form id="uninstall-feedback-form">
                <div class="form-group">
                    <div class="radio-option">
                        <input type="radio" id="reason-price" name="uninstall_reason" value="price">
                        <label for="reason-price">Price ($2.99/month was too high)</label>
                    </div>

                    <div class="radio-option">
                        <input type="radio" id="reason-complexity" name="uninstall_reason" value="complexity">
                        <label for="reason-complexity">Didn't understand features / Too complicated</label>
                    </div>

                    <div class="radio-option">
                        <input type="radio" id="reason-alternative" name="uninstall_reason" value="alternative">
                        <label for="reason-alternative">Found a better alternative</label>
                    </div>

                    <div class="radio-option">
                        <input type="radio" id="reason-bugs" name="uninstall_reason" value="bugs">
                        <label for="reason-bugs">Technical issues / Bugs</label>
                    </div>

                    <div class="radio-option">
                        <input type="radio" id="reason-not-needed" name="uninstall_reason" value="not_needed">
                        <label for="reason-not-needed">No longer needed it</label>
                    </div>

                    <div class="radio-option">
                        <input type="radio" id="reason-other" name="uninstall_reason" value="other">
                        <label for="reason-other">Other reason</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="other-details">If "Other" or you have more details, please specify:</label>
                    <textarea id="other-details" name="other_details" placeholder="Please share your feedback here..."></textarea>
                </div>

                <div id="submit-status"></div>
                <button type="submit">Submit Feedback</button>
            </form>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 SmartParent. Powered by QubitRhythm. All rights reserved.</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const form = document.getElementById('uninstall-feedback-form');
            const submitButton = form.querySelector('button[type="submit"]');
            const submitStatusElement = document.getElementById('submit-status');

            // Extract user data from URL query parameters
            const urlParams = new URLSearchParams(window.location.search);
            const emailFromUrl = urlParams.get('email');
            const statusFromUrl = urlParams.get('status');
            const installTimeFromUrl = urlParams.get('installTime');

            form.addEventListener('submit', async (event) => {
                event.preventDefault(); // Prevent default form submission
                submitButton.disabled = true;
                submitButton.textContent = 'Submitting...';
                submitStatusElement.textContent = 'Processing your feedback...';
                submitStatusElement.style.color = 'var(--text-color)';
                submitStatusElement.style.backgroundColor = 'transparent';

                const formData = new FormData(form);
                const reason = formData.get('uninstall_reason');
                const details = formData.get('other_details');

                // Check if a reason was selected
                if (!reason) {
                    submitStatusElement.textContent = 'Please select a reason before submitting.';
                    submitStatusElement.style.color = 'white';
                    submitStatusElement.style.backgroundColor = 'var(--error-color)';
                    submitButton.disabled = false;
                    submitButton.textContent = 'Submit Feedback';
                    return;
                }

                // Calculate usage duration if install time is available
                let usageDurationDays = null;
                if (installTimeFromUrl) {
                    const installTime = parseInt(installTimeFromUrl);
                    const now = Date.now();
                    const diffTime = Math.abs(now - installTime);
                    usageDurationDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                }

                const payload = {
                    reason: reason,
                    details: details,
                    email: emailFromUrl, // Include email if found in URL
                    userStatus: statusFromUrl, // Include user status if available
                    usageDurationDays: usageDurationDays // Include calculated usage duration
                };

                try {
                    // Adjust URL if your Cloud Function has a specific base path
                    const response = await fetch('/submit-survey', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });

                    if (response.ok) {
                        submitStatusElement.textContent = 'Thank you for your feedback!';
                        submitStatusElement.style.color = 'white';
                        submitStatusElement.style.backgroundColor = 'var(--success-color)';
                        submitButton.textContent = 'Submitted';
                        submitButton.disabled = true;
                    } else {
                        const errorData = await response.json().catch(() => ({}));
                        submitStatusElement.textContent = `Error: ${errorData.error || response.statusText || 'Unknown error'}`;
                        submitStatusElement.style.color = 'white';
                        submitStatusElement.style.backgroundColor = 'var(--error-color)';
                        submitButton.disabled = false;
                        submitButton.textContent = 'Submit Feedback';
                    }
                } catch (error) {
                    console.error('Network error submitting feedback:', error);
                    submitStatusElement.textContent = 'Network error. Please check your connection.';
                    submitStatusElement.style.color = 'white';
                    submitStatusElement.style.backgroundColor = 'var(--error-color)';
                    submitButton.disabled = false;
                    submitButton.textContent = 'Submit Feedback';
                }
            });
        });
    </script>
</body>
</html>
