#!/bin/bash

# SmartParent SSL Certificate Status Check Script

set -e

echo "🔐 SmartParent SSL Certificate Status"
echo "===================================="

# Check kubectl context
CURRENT_CONTEXT=$(kubectl config current-context)
echo "📍 Kubectl context: $CURRENT_CONTEXT"

echo
echo "📋 Certificate Status in Kubernetes:"
kubectl get certificate smartparent-tls -n smartparent -o wide

echo
echo "🔍 Certificate Details:"
kubectl describe certificate smartparent-tls -n smartparent | grep -A 10 "Status:"

echo
echo "🌐 Live SSL Certificate Check:"
echo "   Checking https://smartparent.qubitrhythm.com..."

# Get certificate expiration date
CERT_INFO=$(openssl s_client -connect smartparent.qubitrhythm.com:443 -servername smartparent.qubitrhythm.com 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "   $CERT_INFO"
    
    # Calculate days until expiration
    EXPIRY_DATE=$(echo "$CERT_INFO" | grep "notAfter" | cut -d= -f2)
    EXPIRY_EPOCH=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$EXPIRY_DATE" +%s 2>/dev/null || date -d "$EXPIRY_DATE" +%s 2>/dev/null)
    CURRENT_EPOCH=$(date +%s)
    DAYS_LEFT=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
    
    echo
    if [ $DAYS_LEFT -gt 30 ]; then
        echo "✅ Certificate is healthy ($DAYS_LEFT days remaining)"
    elif [ $DAYS_LEFT -gt 7 ]; then
        echo "⚠️  Certificate expires soon ($DAYS_LEFT days remaining)"
    else
        echo "🚨 Certificate expires very soon ($DAYS_LEFT days remaining)!"
        echo "   Run ./renew-certificate.sh to renew immediately"
    fi
else
    echo "❌ Failed to check live certificate"
fi

echo
echo "🔧 Cert-Manager Status:"
kubectl get clusterissuer letsencrypt-prod -o wide

echo
echo "📊 Recent Certificate Requests:"
kubectl get certificaterequests -n smartparent --sort-by=.metadata.creationTimestamp | tail -5
