/**
 * Trial Notification System Test Script
 * 
 * This script tests the trial expiration notification system for the SmartParent Chrome extension.
 * It can be run in the browser console or as part of the extension's background script for testing.
 */

// Test configuration
const TEST_CONFIG = {
    // Test server URL (adjust as needed)
    SERVER_URL: 'https://test.qubitrhythm.com',
    
    // Test notification intervals (in milliseconds)
    TEST_INTERVALS: {
        '24h': 24 * 60 * 60 * 1000,  // 24 hours
        '12h': 12 * 60 * 60 * 1000,  // 12 hours
        '6h': 6 * 60 * 60 * 1000,    // 6 hours
        '1h': 1 * 60 * 60 * 1000,    // 1 hour
        '30m': 30 * 60 * 1000,       // 30 minutes (for testing)
        '5m': 5 * 60 * 1000,         // 5 minutes (for testing)
        '1m': 1 * 60 * 1000,         // 1 minute (for testing)
        '10s': 10 * 1000             // 10 seconds (for testing)
    }
};

// Test utilities
class TrialNotificationTester {
    constructor() {
        this.testResults = [];
        this.notificationCount = 0;
        this.originalCreateNotification = null;
        this.originalStorageGet = null;
        this.originalStorageSet = null;
        this.mockStorage = {};
    }

    // Initialize test environment
    async init() {
        console.log('🧪 Initializing Trial Notification Test Suite...');
        
        // Mock chrome.notifications.create for testing
        if (typeof chrome !== 'undefined' && chrome.notifications) {
            this.originalCreateNotification = chrome.notifications.create;
            chrome.notifications.create = this.mockCreateNotification.bind(this);
        }

        // Mock chrome.storage for testing
        if (typeof chrome !== 'undefined' && chrome.storage) {
            this.originalStorageGet = chrome.storage.local.get;
            this.originalStorageSet = chrome.storage.local.set;
            
            chrome.storage.local.get = this.mockStorageGet.bind(this);
            chrome.storage.local.set = this.mockStorageSet.bind(this);
        }

        console.log('✅ Test environment initialized');
    }

    // Cleanup test environment
    cleanup() {
        console.log('🧹 Cleaning up test environment...');
        
        if (this.originalCreateNotification) {
            chrome.notifications.create = this.originalCreateNotification;
        }
        
        if (this.originalStorageGet) {
            chrome.storage.local.get = this.originalStorageGet;
        }
        
        if (this.originalStorageSet) {
            chrome.storage.local.set = this.originalStorageSet;
        }

        this.mockStorage = {};
        console.log('✅ Test environment cleaned up');
    }

    // Mock notification creation
    mockCreateNotification(notificationId, options) {
        this.notificationCount++;
        console.log(`📱 Mock Notification Created:`, {
            id: notificationId,
            title: options.title,
            message: options.message,
            type: options.type,
            priority: options.priority,
            requireInteraction: options.requireInteraction
        });
        
        // Simulate successful notification creation
        return Promise.resolve();
    }

    // Mock storage get
    mockStorageGet(keys, callback) {
        const result = {};
        if (Array.isArray(keys)) {
            keys.forEach(key => {
                if (this.mockStorage[key] !== undefined) {
                    result[key] = this.mockStorage[key];
                }
            });
        } else if (typeof keys === 'string') {
            if (this.mockStorage[keys] !== undefined) {
                result[keys] = this.mockStorage[keys];
            }
        } else if (typeof keys === 'object') {
            Object.keys(keys).forEach(key => {
                result[key] = this.mockStorage[key] !== undefined ? this.mockStorage[key] : keys[key];
            });
        }
        
        if (callback) callback(result);
        return Promise.resolve(result);
    }

    // Mock storage set
    mockStorageSet(items, callback) {
        Object.assign(this.mockStorage, items);
        console.log('💾 Mock Storage Updated:', this.mockStorage);
        if (callback) callback();
        return Promise.resolve();
    }

    // Test individual notification trigger
    async testNotificationTrigger(remainingMs, expectedNotificationType) {
        console.log(`\n🔍 Testing notification for ${remainingMs}ms remaining (${this.formatTime(remainingMs)})`);
        
        const initialNotificationCount = this.notificationCount;
        
        try {
            // Call the notification check function (assuming it's available globally)
            if (typeof checkTrialExpirationNotification === 'function') {
                await checkTrialExpirationNotification(remainingMs);
            } else {
                console.warn('⚠️ checkTrialExpirationNotification function not found');
                return false;
            }
            
            const notificationsSent = this.notificationCount - initialNotificationCount;
            
            if (expectedNotificationType && notificationsSent > 0) {
                console.log(`✅ Notification triggered as expected for ${expectedNotificationType}`);
                return true;
            } else if (!expectedNotificationType && notificationsSent === 0) {
                console.log(`✅ No notification triggered as expected`);
                return true;
            } else {
                console.log(`❌ Unexpected notification behavior. Expected: ${expectedNotificationType}, Sent: ${notificationsSent}`);
                return false;
            }
            
        } catch (error) {
            console.error(`❌ Error testing notification trigger:`, error);
            return false;
        }
    }

    // Test notification sequence
    async testNotificationSequence() {
        console.log('\n🔄 Testing notification sequence...');
        
        const testCases = [
            { remaining: TEST_CONFIG.TEST_INTERVALS['24h'] + 1000, expected: null },
            { remaining: TEST_CONFIG.TEST_INTERVALS['24h'] - 1000, expected: '24h' },
            { remaining: TEST_CONFIG.TEST_INTERVALS['12h'] - 1000, expected: '12h' },
            { remaining: TEST_CONFIG.TEST_INTERVALS['6h'] - 1000, expected: '6h' },
            { remaining: TEST_CONFIG.TEST_INTERVALS['1h'] - 1000, expected: '1h' },
            { remaining: 30 * 60 * 1000, expected: null }, // 30 minutes - no notification
            { remaining: 0, expected: null } // Expired - no notification
        ];

        let passedTests = 0;
        
        for (const testCase of testCases) {
            const result = await this.testNotificationTrigger(testCase.remaining, testCase.expected);
            if (result) passedTests++;
            
            // Small delay between tests
            await this.delay(100);
        }
        
        console.log(`\n📊 Sequence Test Results: ${passedTests}/${testCases.length} passed`);
        return passedTests === testCases.length;
    }

    // Test duplicate notification prevention
    async testDuplicatePrevention() {
        console.log('\n🚫 Testing duplicate notification prevention...');
        
        // Set up mock storage with some notifications already sent
        this.mockStorage['trialNotificationsSent'] = {
            '24h': true,
            '12h': true
        };
        
        const initialCount = this.notificationCount;
        
        // Try to trigger notifications that should be blocked
        await this.testNotificationTrigger(TEST_CONFIG.TEST_INTERVALS['24h'] - 1000, null);
        await this.testNotificationTrigger(TEST_CONFIG.TEST_INTERVALS['12h'] - 1000, null);
        
        // Try to trigger a notification that should work
        await this.testNotificationTrigger(TEST_CONFIG.TEST_INTERVALS['6h'] - 1000, '6h');
        
        const finalCount = this.notificationCount;
        const newNotifications = finalCount - initialCount;
        
        if (newNotifications === 1) {
            console.log('✅ Duplicate prevention working correctly');
            return true;
        } else {
            console.log(`❌ Duplicate prevention failed. Expected 1 new notification, got ${newNotifications}`);
            return false;
        }
    }

    // Test notification flag clearing
    async testFlagClearing() {
        console.log('\n🧹 Testing notification flag clearing...');
        
        // Set up mock storage with notifications sent
        this.mockStorage['trialNotificationsSent'] = {
            '24h': true,
            '12h': true,
            '6h': true
        };
        
        // Test clearing when trial is renewed (more than 2 days remaining)
        const moreThan2Days = 3 * 24 * 60 * 60 * 1000; // 3 days
        await this.testNotificationTrigger(moreThan2Days, null);
        
        // Check if flags were cleared
        if (Object.keys(this.mockStorage['trialNotificationsSent'] || {}).length === 0) {
            console.log('✅ Notification flags cleared correctly for trial renewal');
            return true;
        } else {
            console.log('❌ Notification flags not cleared for trial renewal');
            return false;
        }
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting Trial Notification Test Suite\n');
        
        await this.init();
        
        const tests = [
            { name: 'Notification Sequence', test: () => this.testNotificationSequence() },
            { name: 'Duplicate Prevention', test: () => this.testDuplicatePrevention() },
            { name: 'Flag Clearing', test: () => this.testFlagClearing() }
        ];
        
        let passedTests = 0;
        
        for (const { name, test } of tests) {
            console.log(`\n📋 Running test: ${name}`);
            try {
                const result = await test();
                if (result) {
                    console.log(`✅ ${name} - PASSED`);
                    passedTests++;
                } else {
                    console.log(`❌ ${name} - FAILED`);
                }
            } catch (error) {
                console.error(`❌ ${name} - ERROR:`, error);
            }
        }
        
        console.log(`\n🏁 Test Suite Complete: ${passedTests}/${tests.length} tests passed`);
        console.log(`📱 Total notifications created during testing: ${this.notificationCount}`);
        
        this.cleanup();
        
        return passedTests === tests.length;
    }

    // Utility functions
    formatTime(ms) {
        const hours = Math.floor(ms / (1000 * 60 * 60));
        const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
        return `${hours}h ${minutes}m`;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrialNotificationTester;
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
    window.TrialNotificationTester = TrialNotificationTester;
}
