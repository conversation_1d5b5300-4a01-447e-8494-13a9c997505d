{"manifest_version": 3, "name": "Smart Parental Control (Test-Prod)", "version": "2.1.1", "description": "AI web filter & access control. Smart content analysis, screen time management, eye protection & more.", "permissions": ["storage", "alarms", "tabs", "history", "notifications", "http://localhost:8080/*", "https://smartparent.qubitrhythm.com/*", "https://test.qubitrhythm.com/*", "http://*************/*"], "host_permissions": ["<all_urls>"], "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"], "run_at": "document_idle", "all_frames": false, "match_about_blank": false}], "privacy_policy": "https://extension.smartparent.qubitrhythm.com/privacy.html", "externally_connectable": {"matches": ["<all_urls>"]}, "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup/popup.html", "default_icon": {"48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["warning.html", "closed.html", "privacy.html", "subscribe.html", "subscribe.css", "subscribe.js", "success.html", "cancel.html"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; connect-src https://smartparent.qubitrhythm.com https://test.qubitrhythm.com https://extension.smartparent.qubitrhythm.com https://www.google-analytics.com/ http://localhost:32410 http://*************; frame-src https://www.youtube.com/; object-src 'none';"}}