<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trial Notification System Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .warning-btn {
            background-color: #ff9800;
        }
        
        .warning-btn:hover {
            background-color: #e68900;
        }
        
        .danger-btn {
            background-color: #f44336;
        }
        
        .danger-btn:hover {
            background-color: #da190b;
        }
        
        .log-area {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background-color: #4CAF50;
        }
        
        .status-warning {
            background-color: #ff9800;
        }
        
        .status-error {
            background-color: #f44336;
        }
        
        .status-info {
            background-color: #2196F3;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning-box {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Trial Notification System Test</h1>
        
        <div class="info-box">
            <strong>Instructions:</strong> This page helps test the trial notification system. 
            Make sure you have the SmartParent extension installed and open the browser console (F12) to see detailed logs.
        </div>
        
        <div class="test-section">
            <h3>📊 System Status</h3>
            <button onclick="checkSystemStatus()">Check System Status</button>
            <button onclick="checkPermissions()">Check Permissions</button>
            <button onclick="checkCurrentTrial()">Check Trial Status</button>
            <div id="status-display"></div>
        </div>
        
        <div class="test-section">
            <h3>📱 Notification Triggers</h3>
            <p>Test individual notification triggers:</p>
            <button onclick="testNotification('24h')" class="warning-btn">Test 24h Warning</button>
            <button onclick="testNotification('12h')" class="warning-btn">Test 12h Warning</button>
            <button onclick="testNotification('6h')" class="warning-btn">Test 6h Warning</button>
            <button onclick="testNotification('1h')" class="danger-btn">Test 1h Warning</button>
            <button onclick="testNotification('none')">Test No Notification</button>
        </div>
        
        <div class="test-section">
            <h3>🔄 Sequence Tests</h3>
            <p>Test notification sequences and edge cases:</p>
            <button onclick="runSequenceTest()">Run Full Sequence</button>
            <button onclick="testDuplicatePrevention()">Test Duplicate Prevention</button>
            <button onclick="testFlagClearing()">Test Flag Clearing</button>
        </div>
        
        <div class="test-section">
            <h3>⚙️ System Tests</h3>
            <p>Test system integration:</p>
            <button onclick="testAlarmSystem()">Test Alarm System</button>
            <button onclick="testPeriodicCheck()">Test Periodic Check</button>
            <button onclick="testTrialNotificationCheck()">Test Trial Check</button>
        </div>
        
        <div class="test-section">
            <h3>🧹 Utilities</h3>
            <p>Utility functions:</p>
            <button onclick="clearNotificationFlags()">Clear Notification Flags</button>
            <button onclick="clearAllStorage()">Clear All Storage</button>
            <button onclick="createTestNotification()">Create Test Notification</button>
            <button onclick="clearLogs()" class="danger-btn">Clear Logs</button>
        </div>
        
        <div class="warning-box">
            <strong>Note:</strong> Some tests require an active trial. If you don't have an active trial, 
            some notifications may not appear, but the system should still log the test results.
        </div>
        
        <div class="test-section">
            <h3>📝 Test Logs</h3>
            <div id="log-area" class="log-area">Test logs will appear here...\n</div>
        </div>
    </div>

    <script>
        // Logging utility
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('log-area');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            
            // Also log to console
            console.log(`[Test Page] ${message}`);
        }

        function clearLogs() {
            document.getElementById('log-area').textContent = 'Test logs cleared...\n';
        }

        // System status checks
        async function checkSystemStatus() {
            log('Checking system status...');
            
            try {
                // Check if extension context is available
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    log('✓ Chrome extension API available', 'success');
                    
                    // Try to communicate with background script
                    chrome.runtime.sendMessage({type: 'PING'}, (response) => {
                        if (chrome.runtime.lastError) {
                            log('✗ Cannot communicate with background script: ' + chrome.runtime.lastError.message, 'error');
                        } else {
                            log('✓ Background script communication working', 'success');
                        }
                    });
                } else {
                    log('✗ Chrome extension API not available', 'error');
                }
                
                // Check if notification functions are available
                if (typeof checkTrialExpirationNotification === 'function') {
                    log('✓ checkTrialExpirationNotification function available', 'success');
                } else {
                    log('✗ checkTrialExpirationNotification function not available', 'warning');
                }
                
            } catch (error) {
                log('Error checking system status: ' + error.message, 'error');
            }
        }

        async function checkPermissions() {
            log('Checking permissions...');
            
            if (typeof chrome !== 'undefined' && chrome.permissions) {
                chrome.permissions.contains({
                    permissions: ['notifications']
                }, (result) => {
                    if (result) {
                        log('✓ Notifications permission granted', 'success');
                    } else {
                        log('✗ Notifications permission not granted', 'error');
                    }
                });
            } else {
                log('✗ Permissions API not available', 'error');
            }
        }

        async function checkCurrentTrial() {
            log('Checking current trial status...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get(['userStatus', 'email', 'trialUsed'], (result) => {
                    log(`User Status: ${result.userStatus || 'Not set'}`);
                    log(`Email: ${result.email || 'Not set'}`);
                    log(`Trial Used: ${result.trialUsed || false}`);
                    
                    const statusDisplay = document.getElementById('status-display');
                    statusDisplay.innerHTML = `
                        <p><span class="status-indicator ${result.userStatus === 'TrialActive' ? 'status-success' : 'status-warning'}"></span>
                        Status: ${result.userStatus || 'Unknown'}</p>
                        <p>Email: ${result.email || 'Not set'}</p>
                        <p>Trial Used: ${result.trialUsed || false}</p>
                    `;
                });
            } else {
                log('✗ Storage API not available', 'error');
            }
        }

        // Notification tests
        async function testNotification(type) {
            log(`Testing ${type} notification...`);
            
            const intervals = {
                '24h': 23 * 60 * 60 * 1000,  // 23 hours (should trigger 24h warning)
                '12h': 11 * 60 * 60 * 1000,  // 11 hours (should trigger 12h warning)
                '6h': 5 * 60 * 60 * 1000,    // 5 hours (should trigger 6h warning)
                '1h': 30 * 60 * 1000,        // 30 minutes (should trigger 1h warning)
                'none': 25 * 60 * 60 * 1000  // 25 hours (should not trigger)
            };
            
            const remainingMs = intervals[type];
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'TEST_NOTIFICATION',
                    remainingMs: remainingMs
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`Error testing ${type} notification: ` + chrome.runtime.lastError.message, 'error');
                    } else {
                        log(`${type} notification test completed`, 'success');
                    }
                });
            } else {
                log('Cannot test notification - extension API not available', 'error');
            }
        }

        async function runSequenceTest() {
            log('Running full notification sequence test...');
            
            // Clear flags first
            await clearNotificationFlags();
            
            // Test sequence
            const sequence = ['none', '24h', '12h', '6h', '1h'];
            
            for (let i = 0; i < sequence.length; i++) {
                setTimeout(() => {
                    testNotification(sequence[i]);
                }, i * 2000); // 2 second delay between tests
            }
        }

        async function testDuplicatePrevention() {
            log('Testing duplicate notification prevention...');
            
            // Send same notification twice
            testNotification('24h');
            setTimeout(() => {
                testNotification('24h');
                log('Duplicate prevention test completed - check if only one notification appeared', 'warning');
            }, 1000);
        }

        async function testFlagClearing() {
            log('Testing notification flag clearing...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                // Set some flags
                chrome.storage.local.set({
                    'trialNotificationsSent': {
                        '24h': true,
                        '12h': true
                    }
                }, () => {
                    log('Set notification flags');
                    
                    // Test renewal scenario
                    if (chrome.runtime) {
                        chrome.runtime.sendMessage({
                            type: 'TEST_NOTIFICATION',
                            remainingMs: 3 * 24 * 60 * 60 * 1000 // 3 days
                        }, () => {
                            setTimeout(() => {
                                chrome.storage.local.get(['trialNotificationsSent'], (result) => {
                                    const flags = result.trialNotificationsSent || {};
                                    if (Object.keys(flags).length === 0) {
                                        log('✓ Flags cleared correctly on renewal', 'success');
                                    } else {
                                        log('✗ Flags not cleared on renewal', 'error');
                                    }
                                });
                            }, 1000);
                        });
                    }
                });
            }
        }

        // System tests
        async function testAlarmSystem() {
            log('Testing alarm system...');
            
            if (typeof chrome !== 'undefined' && chrome.alarms) {
                chrome.alarms.getAll((alarms) => {
                    const trialAlarm = alarms.find(alarm => alarm.name === 'trialNotificationCheck');
                    
                    if (trialAlarm) {
                        log('✓ Trial notification alarm found', 'success');
                        log(`Alarm scheduled for: ${new Date(trialAlarm.scheduledTime).toLocaleString()}`);
                        log(`Period: ${trialAlarm.periodInMinutes} minutes`);
                    } else {
                        log('✗ Trial notification alarm not found', 'error');
                        log('Available alarms: ' + alarms.map(a => a.name).join(', '));
                    }
                });
            } else {
                log('✗ Alarms API not available', 'error');
            }
        }

        async function testPeriodicCheck() {
            log('Testing periodic status check...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({type: 'TEST_PERIODIC_CHECK'}, (response) => {
                    if (chrome.runtime.lastError) {
                        log('Error testing periodic check: ' + chrome.runtime.lastError.message, 'error');
                    } else {
                        log('Periodic check test completed', 'success');
                    }
                });
            } else {
                log('Cannot test periodic check - extension API not available', 'error');
            }
        }

        async function testTrialNotificationCheck() {
            log('Testing trial notification check...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({type: 'TEST_TRIAL_CHECK'}, (response) => {
                    if (chrome.runtime.lastError) {
                        log('Error testing trial check: ' + chrome.runtime.lastError.message, 'error');
                    } else {
                        log('Trial notification check test completed', 'success');
                    }
                });
            } else {
                log('Cannot test trial check - extension API not available', 'error');
            }
        }

        // Utilities
        async function clearNotificationFlags() {
            log('Clearing notification flags...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.remove(['trialNotificationsSent'], () => {
                    log('✓ Notification flags cleared', 'success');
                });
            } else {
                log('✗ Storage API not available', 'error');
            }
        }

        async function clearAllStorage() {
            log('Clearing all storage...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.clear(() => {
                    chrome.storage.sync.clear(() => {
                        log('✓ All storage cleared', 'success');
                    });
                });
            } else {
                log('✗ Storage API not available', 'error');
            }
        }

        async function createTestNotification() {
            log('Creating test notification...');
            
            if (typeof chrome !== 'undefined' && chrome.notifications) {
                chrome.notifications.create('test-notification-' + Date.now(), {
                    type: 'basic',
                    iconUrl: 'icons/icon128.png',
                    title: 'Test Notification',
                    message: 'This is a test notification from the trial notification system test page.',
                    priority: 2,
                    requireInteraction: true
                }, (notificationId) => {
                    if (chrome.runtime.lastError) {
                        log('Error creating test notification: ' + chrome.runtime.lastError.message, 'error');
                    } else {
                        log('✓ Test notification created successfully', 'success');
                        
                        // Clear after 5 seconds
                        setTimeout(() => {
                            chrome.notifications.clear(notificationId);
                        }, 5000);
                    }
                });
            } else {
                log('✗ Notifications API not available', 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('Trial Notification Test Page loaded');
            log('Click buttons above to test different aspects of the notification system');
            checkSystemStatus();
        });
    </script>
</body>
</html>
