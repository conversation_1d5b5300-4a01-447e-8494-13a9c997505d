#!/bin/bash

NAMESPACE="smartparent"

echo "🔍 Debugging Trial Monitor Setup..."
echo "=================================="

echo "📋 1. Checking CronJob Status:"
echo "=============================="
kubectl get cronjobs -n $NAMESPACE

echo ""
echo "📋 2. Checking All Jobs (including completed/failed):"
echo "===================================================="
kubectl get jobs -n $NAMESPACE --show-all 2>/dev/null || kubectl get jobs -n $NAMESPACE

echo ""
echo "📋 3. Checking Recent Events:"
echo "============================"
kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -10

echo ""
echo "📋 4. Checking CronJob Details:"
echo "=============================="
kubectl describe cronjob smartparent-trial-monitor -n $NAMESPACE

echo ""
echo "📋 5. Checking if Docker Image Exists:"
echo "====================================="
echo "Image: gcr.io/smartparent/smartparent-trial-monitor:latest"

echo ""
echo "📋 6. Checking Secrets:"
echo "====================="
kubectl get secrets -n $NAMESPACE | grep -E "(postgres|cloud-function)"

echo ""
echo "📋 7. Testing Database Connection from Postgres Pod:"
echo "=================================================="
POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app=postgres -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$POSTGRES_POD" ]; then
    echo "✅ Found postgres pod: $POSTGRES_POD"
    echo "Testing connection with correct credentials..."
    kubectl exec -n $NAMESPACE $POSTGRES_POD -- psql -U postgres -d smartparent -c "SELECT 'Database connection successful' as status;" 2>/dev/null || echo "❌ Database connection failed"
else
    echo "❌ No postgres pod found"
fi

echo ""
echo "📋 8. Manual Job Creation Test:"
echo "=============================="
echo "Creating a new manual job to test..."
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
JOB_NAME="debug-trial-check-$TIMESTAMP"

kubectl create job --from=cronjob/smartparent-trial-monitor $JOB_NAME -n $NAMESPACE

echo "✅ Created job: $JOB_NAME"
echo "⏳ Waiting 30 seconds for job to start..."
sleep 30

echo ""
echo "📋 9. Checking New Job Status:"
echo "============================"
kubectl get job $JOB_NAME -n $NAMESPACE
kubectl describe job $JOB_NAME -n $NAMESPACE

echo ""
echo "📋 10. Getting Job Logs:"
echo "======================"
kubectl logs job/$JOB_NAME -n $NAMESPACE 2>/dev/null || echo "❌ No logs available yet"

echo ""
echo "📋 11. Checking Pod Logs if Job Failed:"
echo "====================================="
POD_NAME=$(kubectl get pods -n $NAMESPACE -l job-name=$JOB_NAME -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$POD_NAME" ]; then
    echo "Found pod: $POD_NAME"
    kubectl logs $POD_NAME -n $NAMESPACE 2>/dev/null || echo "❌ No pod logs available"
    kubectl describe pod $POD_NAME -n $NAMESPACE | tail -20
else
    echo "❌ No pod found for job"
fi

echo ""
echo "🧹 Cleaning up debug job..."
kubectl delete job $JOB_NAME -n $NAMESPACE --ignore-not-found=true

echo ""
echo "✅ Debug complete!"
