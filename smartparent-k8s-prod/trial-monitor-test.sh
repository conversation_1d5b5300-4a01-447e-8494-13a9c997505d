#!/bin/bash
set -e

# GCP project details for production environment
PROJECT_ID="smartparent"
NAMESPACE="smartparent"

echo "🧪 Testing SmartParent Trial Monitor in Production..."
echo "=================================================="

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Function to check if kubectl is configured
check_kubectl() {
    if ! kubectl cluster-info &> /dev/null; then
        echo "❌ kubectl is not configured or cluster is not accessible"
        echo "Please run: gcloud container clusters get-credentials [CLUSTER_NAME] --zone [ZONE]"
        exit 1
    fi
    echo "✅ kubectl is configured and cluster is accessible"
}

# Function to check CronJob status
check_cronjob_status() {
    echo ""
    echo "📋 Current CronJob Status:"
    echo "========================="
    kubectl get cronjobs -n $NAMESPACE
    echo ""
    kubectl describe cronjob smartparent-trial-monitor -n $NAMESPACE
}

# Function to check recent jobs
check_recent_jobs() {
    echo ""
    echo "📋 Recent Job Executions:"
    echo "========================"
    kubectl get jobs -n $NAMESPACE | grep trial-monitor || echo "No trial monitor jobs found"
}

# Function to manually trigger the job
trigger_manual_job() {
    echo ""
    echo "🚀 Manually triggering trial monitor job..."
    echo "=========================================="
    
    # Create a unique job name with timestamp
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    JOB_NAME="manual-trial-check-$TIMESTAMP"
    
    kubectl create job --from=cronjob/smartparent-trial-monitor $JOB_NAME -n $NAMESPACE
    
    echo "✅ Manual job created: $JOB_NAME"
    echo "⏳ Waiting for job to complete..."
    
    # Wait for job to complete (timeout after 5 minutes)
    kubectl wait --for=condition=complete job/$JOB_NAME -n $NAMESPACE --timeout=300s
    
    echo "📋 Job logs:"
    kubectl logs job/$JOB_NAME -n $NAMESPACE
    
    echo ""
    echo "🧹 Cleaning up manual job..."
    kubectl delete job $JOB_NAME -n $NAMESPACE
}

# Function to check database connectivity
check_database() {
    echo ""
    echo "🗄️  Testing Database Connectivity:"
    echo "================================="
    
    # Get postgres pod name
    POSTGRES_POD=$(kubectl get pods -n $NAMESPACE -l app=postgres -o jsonpath='{.items[0].metadata.name}')
    
    if [ -z "$POSTGRES_POD" ]; then
        echo "❌ No postgres pod found"
        return 1
    fi
    
    echo "✅ Found postgres pod: $POSTGRES_POD"
    
    # Test database connection and show trial statistics
    echo "📊 Current trial statistics:"
    kubectl exec -n $NAMESPACE $POSTGRES_POD -- psql -U smartparent -d smartparent-db -c "
        SELECT 
            status,
            COUNT(*) as count,
            COUNT(CASE WHEN email IS NOT NULL AND email <> '' THEN 1 END) as with_email,
            COUNT(CASE WHEN trial_reminder_sent_at IS NOT NULL THEN 1 END) as reminders_sent
        FROM installations 
        WHERE trial_used = true
        GROUP BY status
        ORDER BY status;
    " || echo "❌ Database query failed"
}

# Function to check secrets
check_secrets() {
    echo ""
    echo "🔐 Checking Required Secrets:"
    echo "============================"
    
    # Check postgres secrets
    if kubectl get secret postgres-secrets -n $NAMESPACE &> /dev/null; then
        echo "✅ postgres-secrets found"
    else
        echo "❌ postgres-secrets not found"
    fi
    
    # Check cloud function secrets
    if kubectl get secret cloud-function-secrets -n $NAMESPACE &> /dev/null; then
        echo "✅ cloud-function-secrets found"
    else
        echo "❌ cloud-function-secrets not found"
    fi
}

# Main execution
main() {
    check_kubectl
    check_secrets
    check_cronjob_status
    check_recent_jobs
    check_database
    
    echo ""
    read -p "Do you want to manually trigger a trial monitor job for testing? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        trigger_manual_job
    fi
    
    echo ""
    echo "✅ Trial monitor testing completed!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Monitor the CronJob execution at 3:00 AM UTC"
    echo "   2. Check logs regularly: kubectl logs -n $NAMESPACE job/smartparent-trial-monitor-XXXXXX"
    echo "   3. Verify trial expirations are working in your database"
    echo "   4. Test reminder emails are being sent"
}

# Parse command line arguments
case "${1:-}" in
    "status")
        check_kubectl
        check_cronjob_status
        check_recent_jobs
        ;;
    "trigger")
        check_kubectl
        trigger_manual_job
        ;;
    "database")
        check_kubectl
        check_database
        ;;
    "secrets")
        check_kubectl
        check_secrets
        ;;
    *)
        main
        ;;
esac
