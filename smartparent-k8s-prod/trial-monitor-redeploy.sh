#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in trial monitor redeployment script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details for production environment
PROJECT_ID="smartparent"
NAMESPACE="smartparent"
IMAGE_NAME="smartparent-trial-monitor"

echo "🔄 Redeploying SmartParent Trial Monitor with Database Fixes..."
echo "============================================================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Script directory: $SCRIPT_DIR"

# Change to the script directory to ensure relative paths work
cd "$SCRIPT_DIR"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Delete existing CronJob first
echo "Removing existing CronJob..."
kubectl delete cronjob smartparent-trial-monitor -n $NAMESPACE --ignore-not-found=true

# Build and push updated trial monitor image
echo "Building updated trial monitor Docker image..."
docker build -t gcr.io/$PROJECT_ID/$IMAGE_NAME:latest -f trial-monitor.Dockerfile .

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing updated trial monitor image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/$IMAGE_NAME:latest

# Apply the updated CronJob
echo "Deploying updated trial monitor CronJob..."
echo "Using CronJob file: $SCRIPT_DIR/k8s/trial-monitor-cronjob.yaml"
kubectl apply -f "$SCRIPT_DIR/k8s/trial-monitor-cronjob.yaml" -n $NAMESPACE

echo "Verifying CronJob deployment..."
kubectl get cronjobs -n $NAMESPACE
kubectl describe cronjob smartparent-trial-monitor -n $NAMESPACE

echo "✅ Trial Monitor redeployment completed successfully!"
echo ""
echo "📋 What was fixed:"
echo "   - Updated database environment variable names to match main cloud function"
echo "   - Added DATABASE_URL environment variable"
echo "   - Improved error logging for database connection issues"
echo ""
echo "📋 Test the deployment:"
echo "   ./test-trial-monitor.sh trigger"
echo ""
echo "⏰ The CronJob is scheduled to run daily at 3:00 AM UTC"
