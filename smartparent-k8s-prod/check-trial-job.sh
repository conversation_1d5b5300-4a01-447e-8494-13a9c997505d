#!/bin/bash

NAMESPACE="smartparent"
JOB_NAME="manual-trial-check-20250612-025845"

echo "🔍 Checking Trial Monitor Job Status..."
echo "======================================"

echo "📋 Job Status:"
kubectl get job $JOB_NAME -n $NAMESPACE 2>/dev/null || echo "❌ Job not found"

echo ""
echo "📋 Pod Status:"
kubectl get pods -n $NAMESPACE | grep manual-trial-check || echo "No pods found"

echo ""
echo "📋 Job Logs:"
kubectl logs job/$JOB_NAME -n $NAMESPACE 2>/dev/null || echo "❌ No logs available yet"

echo ""
echo "📋 Recent Trial Monitor Jobs:"
kubectl get jobs -n $NAMESPACE | grep trial || echo "No trial monitor jobs found"
