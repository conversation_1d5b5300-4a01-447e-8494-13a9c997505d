#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details
PROJECT_ID="smartparent"
NAMESPACE="smartparent"
IMAGE_NAME="smartparent-trial-monitor"

echo "🔧 Fixing and Redeploying Trial Monitor..."
echo "========================================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Clean up any existing manual jobs first
echo "🧹 Cleaning up existing manual test jobs..."
kubectl delete jobs -n $NAMESPACE -l job-name --field-selector status.successful=1 --ignore-not-found=true
kubectl get jobs -n $NAMESPACE | grep manual-trial-check | awk '{print $1}' | xargs -r kubectl delete job -n $NAMESPACE

# Build and push the fixed image
echo "🔨 Building fixed trial monitor Docker image..."
docker build -t gcr.io/$PROJECT_ID/$IMAGE_NAME:latest -f trial-monitor.Dockerfile .

echo "📤 Pushing fixed image to Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet
docker push gcr.io/$PROJECT_ID/$IMAGE_NAME:latest

# Force update the CronJob to use the new image
echo "🔄 Updating CronJob to use new image..."
kubectl delete cronjob smartparent-trial-monitor -n $NAMESPACE --ignore-not-found=true
kubectl apply -f k8s/trial-monitor-cronjob.yaml -n $NAMESPACE

echo "⏳ Waiting for CronJob to be ready..."
sleep 5

# Test the fix with a new manual job
echo "🧪 Testing the fix with a manual job..."
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
JOB_NAME="fixed-trial-check-$TIMESTAMP"

kubectl create job --from=cronjob/smartparent-trial-monitor $JOB_NAME -n $NAMESPACE

echo "✅ Created test job: $JOB_NAME"
echo "⏳ Waiting for job to complete (timeout: 2 minutes)..."

# Wait for job to complete with timeout
kubectl wait --for=condition=complete job/$JOB_NAME -n $NAMESPACE --timeout=120s

echo ""
echo "📋 Job Results:"
echo "=============="
kubectl get job $JOB_NAME -n $NAMESPACE
echo ""
echo "📋 Job Logs:"
echo "==========="
kubectl logs job/$JOB_NAME -n $NAMESPACE

echo ""
echo "🧹 Cleaning up test job..."
kubectl delete job $JOB_NAME -n $NAMESPACE

echo ""
echo "✅ Fix and redeploy completed successfully!"
echo ""
echo "📋 Summary:"
echo "   - Fixed nodemailer.createTransporter → nodemailer.createTransport"
echo "   - Rebuilt and pushed Docker image"
echo "   - Updated CronJob deployment"
echo "   - Tested successfully"
echo ""
echo "🎯 The trial monitor is now ready for production!"
echo "   - Runs daily at 3:00 AM UTC"
echo "   - Sends reminder emails 1 day before trial expiry"
echo "   - Updates database status for expired trials"
