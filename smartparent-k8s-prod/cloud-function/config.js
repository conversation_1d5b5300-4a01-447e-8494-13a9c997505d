/**
 * Global Configuration Module for Production Environment
 *
 * This module provides a centralized configuration system for the production environment.
 * It uses the production server URL and operates in production mode.
 */

require('dotenv').config();

// Simple and clear: check if we're in test mode or production mode
let IS_TEST_MODE = false;  // Default to production mode
if (process.env.TEST_MODE) {
  if (process.env.TEST_MODE.toLowerCase() == 'true') {
    IS_TEST_MODE = true;  // Only set to test mode if explicitly set to "true"
  }
}

// Do not force using test price IDs in production
const FORCE_TEST_PRICE_IDS = false;

// Configuration object with getters that return appropriate values
const config = {
  // Test mode flag - true for test environment, false for production
  isTestMode: IS_TEST_MODE,

  // Server URL - use production server URL by default
  get serverUrl() {
    return IS_TEST_MODE
      ? (process.env.TEST_SERVER_URL || 'https://test.qubitrhythm.com')
      : (process.env.SERVER_URL || 'https://smartparent.qubitrhythm.com');
  },

  // Database configuration
  get databaseUrl() {
    return IS_TEST_MODE
      ? (process.env.TEST_DATABASE_URL || process.env.DATABASE_URL)
      : process.env.DATABASE_URL;
  },

  get dbSsl() {
    return process.env.DB_SSL === 'true';
  },

  // Stripe configuration
  get stripeSecretKey() {
    return IS_TEST_MODE
      ? (process.env.TEST_STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY)
      : process.env.STRIPE_SECRET_KEY;
  },

  get stripeWebhookSecret() {
    return IS_TEST_MODE
      ? (process.env.TEST_STRIPE_WEBHOOK_SECRET || process.env.STRIPE_WEBHOOK_SECRET)
      : process.env.STRIPE_WEBHOOK_SECRET;
  },

  // Plan price IDs
  get planPriceIdsMonthly() {
    return IS_TEST_MODE || FORCE_TEST_PRICE_IDS
      ? (process.env.TEST_PLAN_PRICE_IDS_MONTHLY || 'price_1ROXMaAR7VlUIrExs1QtF2ma') // Test monthly price ID
      : (process.env.PLAN_PRICE_IDS_MONTHLY || 'price_1RP98oAR7VlUIrExatqrxAEE'); // Production monthly price ID
  },

  get planPriceIdsAnnual() {
    return IS_TEST_MODE || FORCE_TEST_PRICE_IDS
      ? (process.env.TEST_PLAN_PRICE_IDS_ANNUAL || 'price_1ROXIUAR7VlUIrExhmftEcT2') // Test annual price ID
      : (process.env.PLAN_PRICE_IDS_ANNUAL || 'price_1RP99PAR7VlUIrExQiPIcgZl'); // Production annual price ID
  },

  get planPriceIdsStandard() {
    return IS_TEST_MODE
      ? (process.env.TEST_PLAN_PRICE_IDS_STANDARD || process.env.PLAN_PRICE_IDS_STANDARD)
      : process.env.PLAN_PRICE_IDS_STANDARD;
  },

  // Email configuration
  get fromEmail() {
    return process.env.FROM_EMAIL;
  },

  get emailPassword() {
    return process.env.EMAIL_PASSWORD;
  },

  get smtpHost() {
    return process.env.SMTP_HOST || 'smtp.gmail.com';
  },

  get smtpPort() {
    return process.env.SMTP_PORT || 465;
  },

  get smtpSecure() {
    return process.env.SMTP_SECURE === 'true';
  },

  // API configuration
  get grokApiUrl() {
    return process.env.GROK_API_URL;
  },

  get grokApiKey() {
    return process.env.GROK_API_KEY;
  },

  // Other configuration
  get timeout() {
    return process.env.TIMEOUT || 8000;
  },

  get port() {
    return process.env.PORT || 8080;
  },

  get maxRetries() {
    return 2;
  },

  get retryDelay() {
    return 1000;
  },

  // Helper method to get all price IDs
  getPlanPriceIds() {
    return {
      standard: this.planPriceIdsStandard,
      monthly: this.planPriceIdsMonthly,
      annual: this.planPriceIdsAnnual,
    };
  },

  // Helper method to get test price IDs
  getTestPriceIds() {
    return {
      monthly: process.env.TEST_PLAN_PRICE_IDS_MONTHLY || 'price_1ROXMaAR7VlUIrExs1QtF2ma', // Test monthly price ID
      annual: process.env.TEST_PLAN_PRICE_IDS_ANNUAL || 'price_1ROXIUAR7VlUIrExhmftEcT2'   // Test annual price ID
    };
  },

  // Helper method to get production price IDs
  getProductionPriceIds() {
    return {
      monthly: process.env.PLAN_PRICE_IDS_MONTHLY || 'price_1RP98oAR7VlUIrExatqrxAEE', // Production monthly price ID
      annual: process.env.PLAN_PRICE_IDS_ANNUAL || 'price_1RP99PAR7VlUIrExQiPIcgZl'   // Production annual price ID
    };
  }
};

module.exports = config;
