#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in update script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details for production environment
PROJECT_ID="smartparent"
NAMESPACE="smartparent"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Build and push cloud function image for production
echo "Building cloud function Docker image for production..."
docker build -t gcr.io/$PROJECT_ID/cloud-function:latest -f cloud-function/Dockerfile cloud-function

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing cloud function image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/cloud-function:latest

echo "Updating cloud function deployment..."
kubectl apply -f k8s/cloud-function-secrets.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-deployment.yaml -n $NAMESPACE
kubectl apply -f k8s/cloud-function-service.yaml -n $NAMESPACE

# Force restart to ensure new image is pulled
echo "Restarting deployment to ensure new image is used..."
kubectl rollout restart deployment/cloud-function -n $NAMESPACE

# Check rollout status with timeout
echo "Checking rollout status..."
kubectl rollout status deployment/cloud-function -n $NAMESPACE --timeout=180s

echo "Verifying deployments..."
kubectl get pods -n $NAMESPACE -l app=cloud-function
kubectl get services -n $NAMESPACE
kubectl get deployments -n $NAMESPACE

# Check if pods are actually ready
echo "Checking pod readiness..."
kubectl wait --for=condition=ready pod -l app=cloud-function -n $NAMESPACE --timeout=120s

echo "✅ Update completed successfully!"
echo "🔗 The server is accessible at https://smartparent.qubitrhythm.com"
echo "📋 To check logs: kubectl logs -f deployment/cloud-function -n $NAMESPACE"
echo "🔍 To debug: kubectl describe deployment cloud-function -n $NAMESPACE"
