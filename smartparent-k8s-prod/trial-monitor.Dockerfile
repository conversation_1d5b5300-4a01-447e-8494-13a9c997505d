# Trial Monitor Dockerfile for SmartParent
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY cloud-function/package*.json ./

# Install dependencies (including nodemailer and pg)
RUN npm ci --only=production

# Copy the trial monitor script
COPY trial-monitor.js ./

# Copy the config file from cloud-function directory
COPY cloud-function/config.js ./cloud-function/

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Run the trial monitor script
CMD ["node", "trial-monitor.js"]
