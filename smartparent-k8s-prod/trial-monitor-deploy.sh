#!/bin/bash
set -e

# Function to handle errors
handle_error() {
    echo "Error occurred in trial monitor deployment script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# GCP project details for production environment
PROJECT_ID="smartparent"
NAMESPACE="smartparent"
IMAGE_NAME="smartparent-trial-monitor"

echo "🚀 Deploying SmartParent Trial Monitor to Production..."
echo "=================================================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Script directory: $SCRIPT_DIR"

# Change to the script directory to ensure relative paths work
cd "$SCRIPT_DIR"

# Verify required files exist
if [ ! -f "trial-monitor.js" ]; then
    echo "❌ trial-monitor.js not found in $SCRIPT_DIR"
    exit 1
fi

if [ ! -f "trial-monitor.Dockerfile" ]; then
    echo "❌ trial-monitor.Dockerfile not found in $SCRIPT_DIR"
    exit 1
fi

if [ ! -f "k8s/trial-monitor-cronjob.yaml" ]; then
    echo "❌ k8s/trial-monitor-cronjob.yaml not found in $SCRIPT_DIR"
    exit 1
fi

echo "✅ All required files found"

echo "Setting up GCP project..."
gcloud config set project $PROJECT_ID

# Build and push trial monitor image
echo "Building trial monitor Docker image..."
docker build -t gcr.io/$PROJECT_ID/$IMAGE_NAME:latest -f trial-monitor.Dockerfile .

echo "Configuring Docker to use Google Container Registry..."
gcloud auth configure-docker gcr.io --quiet

echo "Pushing trial monitor image to Google Container Registry..."
docker push gcr.io/$PROJECT_ID/$IMAGE_NAME:latest

# Apply the CronJob
echo "Deploying trial monitor CronJob..."
echo "Using CronJob file: $SCRIPT_DIR/k8s/trial-monitor-cronjob.yaml"
kubectl apply -f "$SCRIPT_DIR/k8s/trial-monitor-cronjob.yaml" -n $NAMESPACE

echo "Verifying CronJob deployment..."
kubectl get cronjobs -n $NAMESPACE
kubectl describe cronjob smartparent-trial-monitor -n $NAMESPACE

echo "✅ Trial Monitor deployment completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "   Check CronJob status: kubectl get cronjobs -n $NAMESPACE"
echo "   Check recent jobs: kubectl get jobs -n $NAMESPACE"
echo "   Check job logs: kubectl logs -n $NAMESPACE job/smartparent-trial-monitor-XXXXXX"
echo "   Manually trigger job: kubectl create job --from=cronjob/smartparent-trial-monitor manual-trial-check -n $NAMESPACE"
echo ""
echo "⏰ The CronJob is scheduled to run daily at 3:00 AM UTC"
echo "🔍 Monitor the first execution to ensure everything works correctly"
