const { Pool } = require('pg');
const nodemailer = require('nodemailer');
const path = require('path');
require('dotenv').config();

// Import configuration module
const configPath = path.join(__dirname, 'cloud-function', 'config.js');
let config;
try {
  config = require(configPath);
  console.log(`Configuration loaded from ${configPath}`);
  console.log(`Running in ${config.isTestMode ? 'TEST' : 'PRODUCTION'} mode`);
} catch (err) {
  console.error(`Failed to load configuration from ${configPath}:`, err);
  console.log('Falling back to environment variables');
  // Create a minimal config object with environment variables
  config = {
    isTestMode: process.env.TEST_MODE !== 'false',
    serverUrl: process.env.SERVER_URL,
    fromEmail: process.env.FROM_EMAIL,
    emailPassword: process.env.EMAIL_PASSWORD,
    smtpHost: process.env.SMTP_HOST || 'smtp.gmail.com',
    smtpPort: process.env.SMTP_PORT || 465,
    smtpSecure: process.env.SMTP_SECURE === 'true',
    databaseUrl: process.env.DATABASE_URL,
    dbSsl: process.env.DB_SSL === 'true'
  };
}

// --- Configuration ---
// Parse database URL if provided
let DB_CONFIG = {};
if (config.databaseUrl) {
  try {
    const url = new URL(config.databaseUrl);
    DB_CONFIG = {
      user: url.username || process.env.POSTGRES_USER,
      password: url.password || process.env.POSTGRES_PASSWORD,
      host: url.hostname || process.env.POSTGRES_HOST || 'postgres-service',
      port: url.port || process.env.POSTGRES_PORT || 5432,
      database: url.pathname.substring(1) || process.env.POSTGRES_DB,
      ssl: config.dbSsl ? { rejectUnauthorized: false } : false
    };
  } catch (err) {
    console.error('Failed to parse database URL:', err);
    // Fall back to environment variables (use same names as main cloud function)
    DB_CONFIG = {
      user: process.env.PGUSER || process.env.POSTGRES_USER,
      host: process.env.PGHOST || process.env.POSTGRES_HOST || 'postgres-service',
      database: process.env.PGDATABASE || process.env.POSTGRES_DB,
      password: process.env.PGPASSWORD || process.env.POSTGRES_PASSWORD,
      port: process.env.PGPORT || process.env.POSTGRES_PORT || 5432,
      ssl: config.dbSsl ? { rejectUnauthorized: false } : false
    };
  }
} else {
  // Use environment variables directly (use same names as main cloud function)
  DB_CONFIG = {
    user: process.env.PGUSER || process.env.POSTGRES_USER,
    host: process.env.PGHOST || process.env.POSTGRES_HOST || 'postgres-service',
    database: process.env.PGDATABASE || process.env.POSTGRES_DB,
    password: process.env.PGPASSWORD || process.env.POSTGRES_PASSWORD,
    port: process.env.PGPORT || process.env.POSTGRES_PORT || 5432,
    ssl: config.dbSsl ? { rejectUnauthorized: false } : false
  };
}

const SMTP_CONFIG = {
  host: config.smtpHost,
  port: parseInt(config.smtpPort, 10),
  secure: config.smtpSecure,
  auth: {
    user: config.fromEmail,
    pass: config.emailPassword,
  },
};

const FROM_EMAIL = config.fromEmail;
const SERVER_URL = config.serverUrl;
const REMINDER_DAYS_BEFORE_EXPIRY = 1; // Send reminder 1 day before expiry
const TRIAL_DURATION_DAYS = 7;

// Status Constants
const STATUS_TRIAL_ACTIVE = 'TrialActive';
const STATUS_TRIAL_EXPIRED = 'TrialExpired';
const STATUS_FREE = 'Free';

// --- Email Content ---
const REMINDER_SUBJECT = 'Your SmartParent 7-Day Trial is Ending Tomorrow!';

function getReminderEmailHTML(userEmail, subscriptionUrl) {
  return `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <p>Hello,</p>
        <p>Just a friendly reminder that your 7-day free trial for SmartParent is ending tomorrow.</p>
        <p>We hope you've found SmartParent helpful in creating a safer online environment. To continue protecting your family without interruption, please consider subscribing to one of our plans.</p>
        <p>With a subscription, you'll maintain access to features like:</p>
        <ul>
            <li>Real-time inappropriate content detection</li>
            <li>Game and entertainment site blocking</li>
            <li>Daily browsing history reports</li>
            <li>Peace of mind knowing your child's online activity is monitored</li>
        </ul>
        <p>Ready to subscribe? Click the link below:</p>
        <p><a href="${subscriptionUrl}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 5px;">Subscribe Now</a></p>
        <p>If you have any questions, feel free to reply to this email.</p>
        <p>Best regards,<br>The SmartParent Team</p>
    </div>
  `;
}

// --- Database and Email Logic ---
async function main() {
  console.log('Starting trial monitor job...');

  if (!FROM_EMAIL || !SMTP_CONFIG.auth.pass || !SERVER_URL) {
    console.error('Missing required environment variables: FROM_EMAIL, EMAIL_PASSWORD, SERVER_URL');
    process.exit(1);
  }
  if (!DB_CONFIG.user || !DB_CONFIG.database || !DB_CONFIG.password) {
      console.error('Missing required database environment variables');
      console.error('Required: PGUSER/POSTGRES_USER, PGDATABASE/POSTGRES_DB, PGPASSWORD/POSTGRES_PASSWORD');
      console.error('Current config:', {
        user: DB_CONFIG.user,
        database: DB_CONFIG.database,
        host: DB_CONFIG.host,
        hasPassword: !!DB_CONFIG.password
      });
      process.exit(1);
  }

  console.log(`Database configuration: host=${DB_CONFIG.host}, database=${DB_CONFIG.database}, user=${DB_CONFIG.user}`);
  console.log(`SMTP configuration: host=${SMTP_CONFIG.host}, port=${SMTP_CONFIG.port}, secure=${SMTP_CONFIG.secure}`);
  console.log(`Server URL: ${SERVER_URL}`);

  const pool = new Pool(DB_CONFIG);
  const transporter = nodemailer.createTransport(SMTP_CONFIG);

  try {
    // Verify SMTP connection
    await transporter.verify();
    console.log('SMTP connection verified.');

    // --- 1. Send Expiry Reminders ---
    console.log(`Checking for trials expiring in ${REMINDER_DAYS_BEFORE_EXPIRY} day(s)...`);
    const reminderCutoffDate = new Date();
    reminderCutoffDate.setDate(reminderCutoffDate.getDate() - (TRIAL_DURATION_DAYS - REMINDER_DAYS_BEFORE_EXPIRY));
    // Also check slightly older to catch any missed from previous day run
    const reminderStartDate = new Date(reminderCutoffDate);
    reminderStartDate.setDate(reminderStartDate.getDate() -1);

    const reminderQuery = `
      SELECT email, ip_hash
      FROM installations
      WHERE status = $1
        AND email IS NOT NULL AND email <> ''
        AND COALESCE(activation_timestamp, install_timestamp) <= $2 -- Installed >= 6 days ago
        AND COALESCE(activation_timestamp, install_timestamp) > $3  -- But < 7 days ago
        AND trial_reminder_sent_at IS NULL; -- Only send once
    `;

    const reminderRes = await pool.query(reminderQuery, [
        STATUS_TRIAL_ACTIVE,
        reminderCutoffDate.toISOString(),
        new Date(reminderCutoffDate.getTime() - 24 * 60 * 60 * 1000).toISOString()
    ]);

    console.log(`Found ${reminderRes.rowCount} users for trial expiry reminder.`);

    for (const user of reminderRes.rows) {
      const userEmail = user.email;
      const ipHash = user.ip_hash;
      const subscriptionUrl = `${SERVER_URL}/staticHosting/subscribe.html?email=${encodeURIComponent(userEmail)}`;

      console.log(`Sending reminder email to ${userEmail} (IP Hash: ${ipHash})...`);
      try {
        await transporter.sendMail({
          from: FROM_EMAIL,
          to: userEmail,
          subject: REMINDER_SUBJECT,
          html: getReminderEmailHTML(userEmail, subscriptionUrl),
        });
        console.log(`Reminder email sent successfully to ${userEmail}.`);

        // Mark reminder as sent in DB
        await pool.query(
          'UPDATE installations SET trial_reminder_sent_at = NOW() WHERE ip_hash = $1',
          [ipHash]
        );
        console.log(`Marked reminder sent for ${ipHash}.`);

      } catch (emailError) {
        console.error(`Failed to send reminder email to ${userEmail} (IP Hash: ${ipHash}):`, emailError);
        // Continue to next user, don't mark as sent
      }
    }

    // --- 2. Update Expired Trials for Clean Database Status ---
    console.log('Checking for expired trials to update database status...');
    const expiryCutoffDate = new Date();
    expiryCutoffDate.setDate(expiryCutoffDate.getDate() - TRIAL_DURATION_DAYS); // 7 days ago

    const expireQuery = `
      UPDATE installations
      SET status = $1
      WHERE status = $2
        AND COALESCE(activation_timestamp, install_timestamp) <= $3; -- Started 7 or more days ago
    `;

    const expireRes = await pool.query(expireQuery, [
      STATUS_FREE, // Use Free instead of TrialExpired for consistency with real-time logic
      STATUS_TRIAL_ACTIVE,
      expiryCutoffDate.toISOString(),
    ]);

    console.log(`Updated ${expireRes.rowCount} installations from TrialActive to Free (expired).`);

  } catch (error) {
    console.error('Error during trial monitor job:', error);
    process.exitCode = 1; // Indicate failure
  } finally {
    await pool.end();
    console.log('Database pool closed. Trial monitor job finished.');
  }
}

main();
