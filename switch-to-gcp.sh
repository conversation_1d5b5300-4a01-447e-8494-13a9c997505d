#!/bin/bash

# Switch to GCP Kubernetes context for SmartParent operations

echo "🔄 Switching to GCP SmartParent cluster..."

# Switch to GCP context
kubectl config use-context gke_smartparent_us-central1-f_smartparent-k8s

# Verify the switch
CURRENT_CONTEXT=$(kubectl config current-context)
echo "✅ Current context: $CURRENT_CONTEXT"

# Quick verification that we can access the cluster
echo "🔍 Verifying cluster access..."
kubectl get namespaces | grep smartparent

echo "🎯 Ready for GCP operations!"
echo
echo "Available commands:"
echo "  ./check-certificate.sh    - Check certificate status"
echo "  ./renew-certificate.sh    - Manually renew certificate"
