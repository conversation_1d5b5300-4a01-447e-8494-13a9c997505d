# Trial Notification System - Manual Testing Guide

## Overview
This guide provides step-by-step instructions for manually testing the trial expiration notification system in the SmartParent Chrome extension.

## Prerequisites
- Chrome browser with SmartParent extension installed
- Extension must be in developer mode for console access
- User must have an active trial or ability to start one

## Test Scenarios

### 1. Automated Test Suite

#### Running the Test Script
1. Open Chrome DevTools (F12)
2. Go to the **Console** tab
3. Copy and paste the contents of `test-trial-notifications.js`
4. Run the test suite:
```javascript
const tester = new TrialNotificationTester();
tester.runAllTests();
```

#### Expected Results
- All tests should pass (green checkmarks)
- Console should show detailed test execution logs
- Mock notifications should be created during testing

### 2. Live Trial Testing

#### Setup for Live Testing
1. **Start a trial** (if not already active):
   - Open extension popup
   - Click "Start Premium Trial"
   - Verify trial is active

2. **Access background script console**:
   - Go to `chrome://extensions/`
   - Find SmartParent extension
   - Click "Inspect views: background page"
   - This opens the background script console

#### Test Case 1: Force Notification Triggers
Run these commands in the background script console:

```javascript
// Test 24-hour notification
checkTrialExpirationNotification(23 * 60 * 60 * 1000); // 23 hours remaining

// Test 12-hour notification  
checkTrialExpirationNotification(11 * 60 * 60 * 1000); // 11 hours remaining

// Test 6-hour notification
checkTrialExpirationNotification(5 * 60 * 60 * 1000); // 5 hours remaining

// Test 1-hour notification
checkTrialExpirationNotification(30 * 60 * 1000); // 30 minutes remaining
```

**Expected Results:**
- Browser notifications should appear for each test
- Each notification should have appropriate title and message
- Clicking notification should open subscription page
- Console should log notification creation

#### Test Case 2: Duplicate Prevention
```javascript
// Send same notification twice
checkTrialExpirationNotification(23 * 60 * 60 * 1000);
checkTrialExpirationNotification(23 * 60 * 60 * 1000);

// Check storage to verify flag was set
chrome.storage.local.get(['trialNotificationsSent'], (result) => {
    console.log('Notification flags:', result.trialNotificationsSent);
});
```

**Expected Results:**
- Only one notification should appear
- Storage should show the notification flag is set

#### Test Case 3: Flag Clearing
```javascript
// Set some notification flags
chrome.storage.local.set({
    'trialNotificationsSent': {
        '24h': true,
        '12h': true
    }
}, () => {
    console.log('Flags set');
    
    // Trigger renewal scenario (more than 2 days remaining)
    checkTrialExpirationNotification(3 * 24 * 60 * 60 * 1000);
    
    // Check if flags were cleared
    setTimeout(() => {
        chrome.storage.local.get(['trialNotificationsSent'], (result) => {
            console.log('Flags after renewal:', result.trialNotificationsSent);
        });
    }, 1000);
});
```

**Expected Results:**
- Flags should be cleared when trial has more than 2 days remaining

### 3. Real-Time Testing

#### Test Case 4: Alarm System Integration
```javascript
// Check if trial notification alarm is set
chrome.alarms.getAll((alarms) => {
    const trialAlarm = alarms.find(alarm => alarm.name === 'trialNotificationCheck');
    console.log('Trial notification alarm:', trialAlarm);
});

// Manually trigger the alarm
performTrialNotificationCheck();
```

**Expected Results:**
- Alarm should be present and scheduled
- Manual trigger should check trial status and send notifications if appropriate

#### Test Case 5: Status Change Integration
```javascript
// Simulate status changes
chrome.storage.sync.set({
    userStatus: 'TrialActive'
}, () => {
    console.log('Status set to TrialActive');
    
    // Trigger periodic check
    performPeriodicStatusCheck();
});
```

### 4. Edge Case Testing

#### Test Case 6: Invalid Data Handling
```javascript
// Test with invalid remaining time
checkTrialExpirationNotification(null);
checkTrialExpirationNotification(undefined);
checkTrialExpirationNotification(-1000);
checkTrialExpirationNotification('invalid');
```

**Expected Results:**
- No notifications should be sent
- No errors should be thrown
- Console should handle gracefully

#### Test Case 7: Permission Testing
```javascript
// Check if notifications permission is granted
chrome.permissions.contains({
    permissions: ['notifications']
}, (result) => {
    console.log('Notifications permission granted:', result);
});
```

**Expected Results:**
- Permission should be granted
- If not granted, notifications won't work

## Verification Checklist

### ✅ Functional Tests
- [ ] 24-hour notification triggers correctly
- [ ] 12-hour notification triggers correctly  
- [ ] 6-hour notification triggers correctly
- [ ] 1-hour notification triggers correctly
- [ ] No notifications sent outside trigger windows
- [ ] Duplicate notifications are prevented
- [ ] Notification flags are cleared on trial renewal
- [ ] Notification flags are cleared on subscription
- [ ] Notification flags are cleared on trial expiration

### ✅ Integration Tests
- [ ] Periodic status check includes notification logic
- [ ] Trial notification alarm is created on install/startup
- [ ] Alarm listener handles trial notification alarm
- [ ] Notifications work with real trial data from server

### ✅ User Experience Tests
- [ ] Notifications appear in system notification area
- [ ] Notifications have correct titles and messages
- [ ] Notifications are high priority and require interaction
- [ ] Clicking notifications opens subscription page
- [ ] Notifications are cleared after interaction

### ✅ Error Handling Tests
- [ ] Graceful handling of missing permissions
- [ ] Graceful handling of invalid data
- [ ] Graceful handling of network errors
- [ ] Console errors are logged appropriately

## Troubleshooting

### Common Issues

1. **No notifications appearing**:
   - Check if notifications permission is granted
   - Verify user is in trial status
   - Check console for errors

2. **Duplicate notifications**:
   - Clear storage: `chrome.storage.local.clear()`
   - Restart extension

3. **Notifications not clearing**:
   - Check if subscription/expiration logic is working
   - Manually clear flags: `chrome.storage.local.remove(['trialNotificationsSent'])`

4. **Alarm not firing**:
   - Check alarm list: `chrome.alarms.getAll(console.log)`
   - Restart browser/extension

### Debug Commands

```javascript
// Clear all notification flags
chrome.storage.local.remove(['trialNotificationsSent']);

// Check current storage
chrome.storage.sync.get(null, console.log);
chrome.storage.local.get(null, console.log);

// Check all alarms
chrome.alarms.getAll(console.log);

// Force alarm trigger
chrome.alarms.onAlarm.dispatch({name: 'trialNotificationCheck'});
```

## Success Criteria

The trial notification system is working correctly if:
1. All automated tests pass
2. Manual tests produce expected results
3. Real trial scenarios trigger appropriate notifications
4. No duplicate notifications are sent
5. System integrates properly with existing extension functionality
6. User experience is smooth and non-intrusive
