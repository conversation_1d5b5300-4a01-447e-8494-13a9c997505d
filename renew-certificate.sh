#!/bin/bash

# SmartParent SSL Certificate Renewal Script
# This script manually renews the SSL certificate for smartparent.qubitrhythm.com

set -e

echo "🔐 SmartParent SSL Certificate Renewal Script"
echo "============================================="

# Check if we're connected to the right cluster
CURRENT_CONTEXT=$(kubectl config current-context)
echo "📍 Current kubectl context: $CURRENT_CONTEXT"

if [[ ! "$CURRENT_CONTEXT" =~ "smartparent" ]]; then
    echo "⚠️  Warning: Current context doesn't seem to be the SmartParent cluster"
    echo "   Make sure you're connected to the right GCP cluster"
    read -p "   Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo
echo "📋 Current certificate status:"
kubectl get certificate smartparent-tls -n smartparent -o wide

echo
echo "🔄 Triggering certificate renewal..."

# Method 1: Delete and recreate certificate (forces immediate renewal)
echo "   Deleting current certificate..."
kubectl delete certificate smartparent-tls -n smartparent

echo "   Recreating certificate..."
kubectl apply -f smartparent-k8s-prod/k8s/cert-issuer.yaml

echo
echo "⏳ Waiting for certificate to be issued..."
echo "   This may take 1-2 minutes..."

# Wait for certificate to be ready
for i in {1..60}; do
    STATUS=$(kubectl get certificate smartparent-tls -n smartparent -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null || echo "False")
    if [ "$STATUS" = "True" ]; then
        echo "✅ Certificate successfully renewed!"
        break
    fi
    echo "   Waiting... ($i/60)"
    sleep 5
done

if [ "$STATUS" != "True" ]; then
    echo "❌ Certificate renewal timed out. Check status manually:"
    echo "   kubectl get certificate smartparent-tls -n smartparent -o wide"
    echo "   kubectl describe certificate smartparent-tls -n smartparent"
    exit 1
fi

echo
echo "📋 New certificate details:"
kubectl get certificate smartparent-tls -n smartparent -o wide

echo
echo "🧪 Testing SSL certificate..."
curl -s -I https://smartparent.qubitrhythm.com | head -1

echo
echo "✅ Certificate renewal completed successfully!"
echo "   New certificate is valid for 90 days"
echo "   Next automatic renewal will occur in ~60 days"
